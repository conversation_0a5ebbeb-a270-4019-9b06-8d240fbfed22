import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { ConfigProvider, theme } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import enUS from 'antd/locale/en_US'
import { Toaster } from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { ErrorBoundary } from '@/components/ErrorBoundary'
import { NotificationProvider } from '@/components/NotificationProvider'
import { Layout } from '@/components/Layout/Layout'
import { Home } from '@/pages/Home'
import { NewHome } from '@/pages/NewHome'
import { Login } from '@/pages/Login'
import { Register } from '@/pages/Register'
import { Dashboard } from '@/pages/Dashboard'
import { Resources } from '@/pages/Resources'
import { Creator } from '@/pages/Creator'
import '@/i18n'

function App() {
  const { i18n } = useTranslation()

  // 根据当前语言选择Ant Design语言包
  const getAntdLocale = () => {
    switch (i18n.language) {
      case 'en-US':
        return enUS
      case 'zh-CN':
      default:
        return zhCN
    }
  }

  return (
    <ErrorBoundary>
      <ConfigProvider
        locale={getAntdLocale()}
        theme={{
          algorithm: theme.defaultAlgorithm,
          token: {
            colorPrimary: '#1890ff',
            borderRadius: 8,
            fontFamily: 'Inter, system-ui, sans-serif',
          },
        }}
      >
        <NotificationProvider>
          <Router>
            <Routes>
              <Route path="/" element={<NewHome />} />
              <Route path="/old-home" element={
                <Layout>
                  <Home />
                </Layout>
              } />
              <Route path="/resources" element={
                <Layout>
                  <Resources />
                </Layout>
              } />
              <Route path="/categories" element={
                <Layout>
                  <div>Categories Page (Coming Soon)</div>
                </Layout>
              } />
              <Route path="/login" element={
                <Layout>
                  <Login />
                </Layout>
              } />
              <Route path="/register" element={
                <Layout>
                  <Register />
                </Layout>
              } />
              <Route path="/dashboard" element={
                <Layout>
                  <Dashboard />
                </Layout>
              } />
              <Route path="/creator/:creatorId" element={
                <Layout>
                  <Creator />
                </Layout>
              } />
              <Route path="/resource/:resourceId" element={
                <Layout>
                  <div style={{ padding: '24px', color: '#fff' }}>
                    <h2>资源详情页</h2>
                    <p>资源详情页面开发中...</p>
                    <p>资源ID: {window.location.pathname.split('/')[2]}</p>
                  </div>
                </Layout>
              } />
              <Route path="*" element={
                <Layout>
                  <div>404 - Page Not Found</div>
                </Layout>
              } />
            </Routes>
            <Toaster position="top-right" />
          </Router>
        </NotificationProvider>
      </ConfigProvider>
    </ErrorBoundary>
  )
}

export default App
