import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { ConfigProvider, theme } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import enUS from 'antd/locale/en_US'
import { Toaster } from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { ErrorBoundary } from '@/components/ErrorBoundary'
import { NotificationProvider } from '@/components/NotificationProvider'
import { Layout } from '@/components/Layout/Layout'
import { Home } from '@/pages/Home'
import { NewHome } from '@/pages/NewHome'
import { Login } from '@/pages/Login'
import { Register } from '@/pages/Register'
import { Dashboard } from '@/pages/Dashboard'
import { Resources } from '@/pages/Resources'
import { Creator } from '@/pages/Creator'
import About from '@/pages/About'
import FooterTest from '@/pages/FooterTest'
import '@/i18n'

function App() {
  const { i18n } = useTranslation()

  // 根据当前语言选择Ant Design语言包
  const getAntdLocale = () => {
    switch (i18n.language) {
      case 'en-US':
        return enUS
      case 'zh-CN':
      default:
        return zhCN
    }
  }

  return (
    <ErrorBoundary>
      <ConfigProvider
        locale={getAntdLocale()}
        theme={{
          algorithm: theme.defaultAlgorithm,
          token: {
            colorPrimary: '#1890ff',
            borderRadius: 8,
            fontFamily: 'Inter, system-ui, sans-serif',
          },
        }}
      >
        <NotificationProvider>
          <Router>
            <Routes>
              <Route path="/" element={<NewHome />} />
              <Route path="/old-home" element={
                <Layout>
                  <Home />
                </Layout>
              } />
              <Route path="/resources" element={
                <Layout>
                  <Resources />
                </Layout>
              } />
              <Route path="/categories" element={
                <Layout>
                  <div>Categories Page (Coming Soon)</div>
                </Layout>
              } />
              <Route path="/login" element={
                <Layout>
                  <Login />
                </Layout>
              } />
              <Route path="/register" element={
                <Layout>
                  <Register />
                </Layout>
              } />
              <Route path="/dashboard" element={
                <Layout>
                  <Dashboard />
                </Layout>
              } />
              <Route path="/creator/:creatorId" element={
                <Layout>
                  <Creator />
                </Layout>
              } />
              <Route path="/resource/:resourceId" element={
                <Layout>
                  <div style={{ padding: '24px', color: '#fff' }}>
                    <h2>资源详情页</h2>
                    <p>资源详情页面开发中...</p>
                    <p>资源ID: {window.location.pathname.split('/')[2]}</p>
                  </div>
                </Layout>
              } />

              {/* Footer Navigation Routes */}
              <Route path="/about" element={
                <Layout>
                  <About />
                </Layout>
              } />
              <Route path="/footer-test" element={
                <Layout>
                  <FooterTest />
                </Layout>
              } />
              <Route path="/careers" element={
                <Layout>
                  <div style={{ padding: '24px', color: '#fff' }}>
                    <h2>招贤纳士</h2>
                    <p>加入我们的团队，共同推动AI技术发展...</p>
                  </div>
                </Layout>
              } />
              <Route path="/news" element={
                <Layout>
                  <div style={{ padding: '24px', color: '#fff' }}>
                    <h2>资讯及活动中心</h2>
                    <p>最新资讯和活动信息...</p>
                  </div>
                </Layout>
              } />
              <Route path="/support" element={
                <Layout>
                  <div style={{ padding: '24px', color: '#fff' }}>
                    <h2>技术支持与交流</h2>
                    <p>技术支持和社区交流...</p>
                  </div>
                </Layout>
              } />
              <Route path="/help" element={
                <Layout>
                  <div style={{ padding: '24px', color: '#fff' }}>
                    <h2>帮助中心</h2>
                    <p>常见问题和使用指南...</p>
                  </div>
                </Layout>
              } />
              <Route path="/contact" element={
                <Layout>
                  <div style={{ padding: '24px', color: '#fff' }}>
                    <h2>联系我们</h2>
                    <p>联系方式和客服信息...</p>
                  </div>
                </Layout>
              } />
              <Route path="/terms" element={
                <Layout>
                  <div style={{ padding: '24px', color: '#fff' }}>
                    <h2>用户协议</h2>
                    <p>用户服务协议条款...</p>
                  </div>
                </Layout>
              } />
              <Route path="/privacy" element={
                <Layout>
                  <div style={{ padding: '24px', color: '#fff' }}>
                    <h2>隐私条款</h2>
                    <p>隐私保护政策...</p>
                  </div>
                </Layout>
              } />
              <Route path="/usage" element={
                <Layout>
                  <div style={{ padding: '24px', color: '#fff' }}>
                    <h2>使用条款</h2>
                    <p>平台使用条款和规范...</p>
                  </div>
                </Layout>
              } />
              <Route path="/trademark" element={
                <Layout>
                  <div style={{ padding: '24px', color: '#fff' }}>
                    <h2>商标</h2>
                    <p>商标信息和知识产权...</p>
                  </div>
                </Layout>
              } />
              <Route path="/ads" element={
                <Layout>
                  <div style={{ padding: '24px', color: '#fff' }}>
                    <h2>关于我们的广告</h2>
                    <p>广告政策和合作信息...</p>
                  </div>
                </Layout>
              } />

              <Route path="*" element={
                <Layout>
                  <div>404 - Page Not Found</div>
                </Layout>
              } />
            </Routes>
            <Toaster position="top-right" />
          </Router>
        </NotificationProvider>
      </ConfigProvider>
    </ErrorBoundary>
  )
}

export default App
