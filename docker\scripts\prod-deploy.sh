#!/bin/bash

# AIGC Service Hub - Production Deployment Script

echo "🚀 Deploying AIGC Service Hub to Production..."

# Check if production environment file exists
if [ ! -f .env.production ]; then
    echo "❌ .env.production file not found!"
    echo "Please create .env.production with production configuration"
    exit 1
fi

# Load production environment
export $(cat .env.production | xargs)

# Backup current deployment (if exists)
if [ -d "backup" ]; then
    echo "📦 Creating backup of current deployment..."
    timestamp=$(date +%Y%m%d_%H%M%S)
    mkdir -p backups
    tar -czf "backups/backup_${timestamp}.tar.gz" -C backup .
fi

# Pull latest images
echo "📦 Pulling latest production images..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml pull

# Build production images
echo "🔨 Building production images..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml build --no-cache

# Run database migrations
echo "🗄️  Running database migrations..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml run --rm backend npm run migrate

# Start production services
echo "🚀 Starting production services..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 30

# Health check
echo "🏥 Performing health checks..."
if curl -f http://localhost/health > /dev/null 2>&1; then
    echo "✅ Application is healthy and running!"
else
    echo "❌ Health check failed!"
    echo "📋 Checking logs..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs --tail=50
    exit 1
fi

# Show running services
echo "📊 Production services status:"
docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps

echo "✅ Production deployment completed successfully!"
echo "🌐 Application URL: https://your-domain.com"
echo ""
echo "📋 Useful commands:"
echo "   View logs: docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs -f"
echo "   Scale backend: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --scale backend=3"
echo "   Stop services: docker-compose -f docker-compose.yml -f docker-compose.prod.yml down"
