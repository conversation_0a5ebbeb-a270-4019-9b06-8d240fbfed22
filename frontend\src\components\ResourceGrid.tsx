import React from 'react'
import { Row, Col, Skeleton } from 'antd'
import { ResourceCard } from './ResourceCard'
import { SearchEmptyState } from './EmptyState'
import { Resource } from '@/types'

interface ResourceGridProps {
  resources: Resource[]
  loading?: boolean
  onPurchase?: (resourceId: string) => void
  showPurchaseButton?: boolean
  emptyMessage?: string
}

export const ResourceGrid: React.FC<ResourceGridProps> = ({
  resources,
  loading = false,
  onPurchase,
  showPurchaseButton = true,
  emptyMessage = 'No resources found'
}) => {
  if (loading) {
    return (
      <Row gutter={[24, 24]}>
        {Array.from({ length: 8 }).map((_, index) => (
          <Col key={index} xs={24} sm={12} lg={8} xl={6}>
            <Skeleton.Image style={{ width: '100%', height: 200 }} />
            <Skeleton active paragraph={{ rows: 4 }} />
          </Col>
        ))}
      </Row>
    )
  }

  if (resources.length === 0) {
    return (
      <SearchEmptyState
        title={emptyMessage}
        description="Try adjusting your search criteria or browse different categories."
        actionText="Clear Filters"
        showAction={false}
      />
    )
  }

  return (
    <Row gutter={[24, 24]}>
      {resources.map((resource) => (
        <Col key={resource.id} xs={24} sm={12} lg={8} xl={6}>
          <ResourceCard
            resource={resource}
            onPurchase={onPurchase}
            showPurchaseButton={showPurchaseButton}
          />
        </Col>
      ))}
    </Row>
  )
}
