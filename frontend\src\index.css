/* Global styles for AIGC Service Hub */
body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #1a1a1a;
  color: #fff;
}

#root {
  min-height: 100vh;
  background-color: #1a1a1a;
}

/* Ant Design customizations */
.ant-layout {
  background: #1a1a1a;
}

.ant-card {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  background-color: #2a2a2a;
  border-color: #3a3a3a;
}

.ant-card .ant-card-body {
  background-color: #2a2a2a;
  color: #fff;
}

.ant-card .ant-card-meta-title {
  color: #fff;
}

.ant-card .ant-card-meta-description {
  color: #ccc;
}

.ant-btn {
  border-radius: 8px;
}

.ant-btn-primary {
  background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%);
  border-color: #8B5CF6;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #7C3AED 0%, #9333EA 100%);
  border-color: #7C3AED;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
}

.ant-input {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
  color: #fff;
}

.ant-input::placeholder {
  color: #888;
}

.ant-input:focus {
  border-color: #8B5CF6;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

.ant-menu-dark {
  background-color: transparent;
}

.ant-menu-dark .ant-menu-item-selected {
  background-color: rgba(139, 92, 246, 0.2);
  color: #8B5CF6;
}

.ant-tag {
  border-radius: 6px;
}

/* Search input styling */
.ant-input-search .ant-input {
  background-color: #2a2a2a !important;
  border-color: #3a3a3a !important;
  color: #fff !important;
  font-size: 14px !important;
}

.ant-input-search .ant-input::placeholder {
  color: #888 !important;
  font-size: 14px !important;
}

.ant-input-search .ant-btn {
  background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%) !important;
  border-color: #8B5CF6 !important;
}

/* Carousel dots styling */
.ant-carousel .slick-dots li button {
  background: #3a3a3a;
}

.ant-carousel .slick-dots li.slick-active button {
  background: #8B5CF6;
}

/* Menu item hover effects */
.ant-menu-dark .ant-menu-item:hover {
  background-color: rgba(139, 92, 246, 0.1);
  color: #8B5CF6;
}

/* Card hover effects */
.ant-card:hover {
  transform: translateY(-2px);
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.2);
}

/* Typography in dark theme */
.ant-typography {
  color: #fff;
}

.ant-typography.ant-typography-secondary {
  color: #888 !important;
}

/* Spin component styling */
.ant-spin .ant-spin-dot-item {
  background-color: #8B5CF6;
}

/* Avatar styling */
.ant-avatar {
  border: 2px solid #3a3a3a;
}

/* Badge styling */
.ant-badge .ant-badge-count {
  background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%);
  border-color: #8B5CF6;
}

/* Divider styling */
.ant-divider {
  border-color: #3a3a3a;
}

/* Space component styling */
.ant-space {
  color: #fff;
}

/* Row and Col styling */
.ant-row {
  color: #fff;
}

/* Additional card meta styling */
.ant-card-meta .ant-card-meta-title {
  color: #fff !important;
}

.ant-card-meta .ant-card-meta-description {
  color: #ccc !important;
}

/* Ensure all text in cards is properly colored */
.ant-card .ant-card-body * {
  color: inherit;
}

/* Fix for any remaining white backgrounds */
.ant-card-actions {
  background-color: #2a2a2a !important;
  border-top: 1px solid #3a3a3a !important;
}

.ant-card-actions > li {
  color: #888 !important;
}

/* Carousel styling */
.ant-carousel {
  color: #fff;
}

/* Ensure proper text color inheritance */
* {
  box-sizing: border-box;
}

/* Override any remaining light theme elements */
.ant-layout-content {
  background-color: #1a1a1a !important;
}

/* Dropdown menu styling */
.ant-dropdown {
  background-color: #2a2a2a !important;
  border: 1px solid #3a3a3a !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3) !important;
}

.ant-dropdown .ant-dropdown-menu {
  background-color: #2a2a2a !important;
  border: none !important;
}

.ant-dropdown .ant-dropdown-menu-item {
  color: #fff !important;
  background-color: transparent !important;
}

.ant-dropdown .ant-dropdown-menu-item:hover {
  background-color: rgba(139, 92, 246, 0.1) !important;
  color: #8B5CF6 !important;
}

/* Button hover effects */
.ant-btn:hover {
  border-color: #8B5CF6 !important;
  color: #8B5CF6 !important;
}

.ant-btn-default {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
  color: #fff;
}

.ant-btn-default:hover {
  background-color: rgba(139, 92, 246, 0.1) !important;
  border-color: #8B5CF6 !important;
  color: #8B5CF6 !important;
}

/* Category and Style tag hover effects */
.category-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3) !important;
}

.style-tag:hover {
  background-color: rgba(139, 92, 246, 0.8) !important;
  border-color: #8B5CF6 !important;
  color: #fff !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3) !important;
}

/* Tooltip styling */
.ant-tooltip {
  z-index: 1050;
}

.ant-tooltip .ant-tooltip-inner {
  background-color: #2a2a2a;
  color: #fff;
  border: 1px solid #3a3a3a;
  border-radius: 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.ant-tooltip .ant-tooltip-arrow::before {
  background-color: #2a2a2a;
  border: 1px solid #3a3a3a;
}

/* MainNavigation menu styles - 与TopHeader保持一致 */
.main-navigation-menu {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
}

.main-navigation-menu .ant-menu-item {
  transition: all 0.2s ease !important;
  border-radius: 6px !important;
  margin: 0 2px !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
  font-size: 16px !important;
  font-weight: 200 !important;
  color: #fff !important;
}

.main-navigation-menu .ant-menu-item a {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
  font-size: 16px !important;
  font-weight: 200 !important;
  color: #fff !important;
  text-decoration: none !important;
}

.main-navigation-menu .ant-menu-item .anticon {
  font-size: 16px !important;
  color: #fff !important;
}

.main-navigation-menu .ant-menu-item:hover {
  background-color: rgba(139, 92, 246, 0.1) !important;
  color: #8B5CF6 !important;
}

.main-navigation-menu .ant-menu-item:hover a {
  color: #8B5CF6 !important;
}

.main-navigation-menu .ant-menu-item:hover .anticon {
  color: #8B5CF6 !important;
}

.main-navigation-menu .ant-menu-item.ant-menu-item-selected {
  background-color: rgba(139, 92, 246, 0.15) !important;
  color: #8B5CF6 !important;
  border-bottom: none !important;
}

.main-navigation-menu .ant-menu-item.ant-menu-item-selected a {
  color: #8B5CF6 !important;
}

.main-navigation-menu .ant-menu-item.ant-menu-item-selected .anticon {
  color: #8B5CF6 !important;
}

/* Ensure menu items maintain left alignment */
.ant-menu-horizontal {
  border-bottom: none !important;
}

.ant-menu-horizontal::after {
  display: none !important;
}

/* FeaturedSection card hover effects - 由JavaScript控制 */
.featured-work-card {
  transition: all 0.3s ease;
}

.featured-work-card:hover {
  border-color: #8B5CF6;
}

.featured-work-card:hover .featured-image-placeholder {
  background-color: #4a4a4a;
}

/* Featured card layout - adjusted heights */
.featured-work-card {
  height: 330px !important; /* 从320px增加到330px */
}

.featured-work-card .featured-image-area {
  height: 240px !important; /* 保持图片区域高度不变 */
}

.featured-work-card .featured-content-area {
  height: 90px !important; /* 从80px增加到90px */
}

/* Responsive adjustments for featured cards */
@media (max-width: 768px) {
  .featured-work-card {
    height: 290px !important; /* 从280px增加到290px */
  }

  .featured-work-card .featured-image-area {
    height: 210px !important; /* 保持图片区域高度不变 */
  }

  .featured-work-card .featured-content-area {
    height: 80px !important; /* 从70px增加到80px */
  }
}

@media (max-width: 480px) {
  .featured-work-card {
    height: 270px !important; /* 从260px增加到270px */
  }

  .featured-work-card .featured-image-area {
    height: 195px !important; /* 保持图片区域高度不变 */
  }

  .featured-work-card .featured-content-area {
    height: 75px !important; /* 从65px增加到75px */
  }
}

/* HeroBanner carousel hover effects - 与精选作品区保持一致 */
.hero-banner-slide:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.15);
  border-color: #8B5CF6;
}

/* Hero creator card hover effects - 与精选作品区保持一致 */
.hero-creator-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.15);
  border-color: #8B5CF6;
}

.hero-creator-card:hover .ant-avatar {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

/* Carousel dots styling for hero banner */
.ant-carousel .ant-carousel-dots {
  bottom: 16px;
}

.ant-carousel .ant-carousel-dots li button {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 8px;
  height: 8px;
}

.ant-carousel .ant-carousel-dots li.ant-carousel-dot-active button {
  background: #8B5CF6;
  width: 12px;
  height: 12px;
}

/* Loading animation for hero leaderboard */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.hero-leaderboard-loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* 英雄榜高度精确控制 */
.hero-leaderboard-card {
  height: 300px !important;
}

.hero-leaderboard-card .ant-card-head {
  min-height: 40px !important;
  padding: 0 12px !important;
}

.hero-leaderboard-card .ant-card-body {
  height: calc(300px - 40px) !important;
  padding: 8px !important;
  overflow: hidden !important;
}

/* Responsive adjustments for hero banner */
@media (max-width: 768px) {
  .hero-banner-slide {
    height: 200px !important;
  }

  .hero-banner-slide h2 {
    font-size: 18px !important;
  }

  .hero-creator-card {
    padding: 4px !important;
  }

  .hero-creator-card .ant-avatar {
    width: 20px !important;
    height: 20px !important;
  }

  .hero-leaderboard-card {
    height: 200px !important;
  }

  .hero-leaderboard-card .ant-card-body {
    height: calc(200px - 40px) !important;
  }
}

/* WaterfallGrid resource card hover effects - 与其他组件保持一致 */
.waterfall-resource-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.15);
  border-color: #8B5CF6;
}

/* Creator avatar hover effect */
.creator-avatar:hover {
  transform: scale(1.1);
}

/* Responsive adjustments for waterfall grid */
@media (max-width: 768px) {
  .waterfall-resource-card {
    margin-bottom: 12px !important;
  }

  .waterfall-resource-card .creator-avatar {
    width: 20px !important;
    height: 20px !important;
  }
}

/* TopHeader adjustments for 100px height */
.ant-layout-header {
  height: 100px !important;
  line-height: 100px !important;
}

/* Search input transparent background */
.ant-input-search .ant-input {
  background-color: transparent !important;
}

.ant-input-search .ant-input:focus {
  background-color: rgba(42, 42, 42, 0.3) !important;
}

/* TopHeader font weight normalization */
.ant-layout-header .ant-btn {
  font-weight: normal !important;
}

.ant-layout-header .ant-input {
  font-weight: normal !important;
}

.ant-layout-header .ant-typography {
  font-weight: normal !important;
}

/* Responsive search box */
.responsive-search {
  width: 600px !important;
}

/* Search container responsive */
.search-container {
  max-width: calc(100vw - 400px);
}

/* Logo hover effect */
.logo-hover:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* Dropdown menu styling */
.ant-dropdown-menu {
  background-color: #2a2a2a !important;
  border: 1px solid #3a3a3a !important;
}

.ant-dropdown-menu-item {
  color: #fff !important;
}

.ant-dropdown-menu-item:hover {
  background-color: rgba(139, 92, 246, 0.1) !important;
  color: #8B5CF6 !important;
}

/* Responsive adjustments for header */

/* 桌面端 (≥1200px) - 保持600px宽度 */
@media (min-width: 1200px) {
  .responsive-search {
    width: 600px !important;
  }

  .search-container {
    max-width: 700px;
  }
}

/* 平板端 (768px-1199px) - 调整为400px宽度 */
@media (min-width: 768px) and (max-width: 1199px) {
  .responsive-search {
    width: 400px !important;
  }

  .search-container {
    max-width: 500px;
  }

  .ant-layout-header {
    padding: 0 16px !important;
  }
}

/* 移动端 (<768px) - 调整为屏幕宽度的80%，最小200px */
@media (max-width: 767px) {
  .ant-layout-header {
    height: 80px !important;
    line-height: 80px !important;
    padding: 0 12px !important;
  }

  .responsive-search {
    width: 80vw !important;
    min-width: 200px !important;
    max-width: 300px !important;
  }

  .search-container {
    max-width: calc(100vw - 200px);
    margin: 0 8px;
  }

  .ant-btn {
    padding: 4px 8px !important;
    font-size: 12px !important;
  }

  /* 移动端按钮文字简化 */
  .mobile-btn-text {
    display: none;
  }
}

/* 超小屏幕 (<480px) */
@media (max-width: 479px) {
  .responsive-search {
    width: 75vw !important;
    min-width: 180px !important;
    max-width: 250px !important;
  }

  .search-container {
    max-width: calc(100vw - 150px);
  }

  .ant-layout-header {
    padding: 0 8px !important;
  }
}

/* Custom utility classes */
.text-center {
  text-align: center;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.space-x-2 > * + * {
  margin-left: 8px;
}

.space-x-4 > * + * {
  margin-left: 16px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-2 {
  margin-top: 8px;
}

.mt-4 {
  margin-top: 16px;
}

/* LOGO响应式样式 */
.logo-responsive {
  height: 60px;
  width: auto;
  cursor: pointer;
  transition: transform 0.2s ease;
}

/* 平板端LOGO调整 */
@media (max-width: 768px) {
  .logo-responsive {
    height: 50px;
  }

  /* 调整页眉高度以适配平板端 */
  .ant-layout-header {
    height: 90px !important;
  }
}

/* 移动端LOGO调整 */
@media (max-width: 480px) {
  .logo-responsive {
    height: 45px;
  }

  /* 调整页眉高度以适配移动端 */
  .ant-layout-header {
    height: 80px !important;
  }
}
