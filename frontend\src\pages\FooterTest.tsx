import React from 'react'
import { Typography, Space } from 'antd'

const { Title, Paragraph } = Typography

const FooterTest: React.FC = () => {
  return (
    <div style={{ 
      padding: '24px', 
      color: '#fff', 
      maxWidth: '1200px', 
      margin: '0 auto',
      minHeight: '60vh' // 确保页面有足够高度来显示Footer
    }}>
      <Title level={2} style={{ color: '#fff', marginBottom: '24px' }}>Footer 组件测试页面</Title>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Paragraph style={{ fontSize: '16px', color: '#ccc' }}>
          这是一个用于测试Footer组件的页面。请滚动到页面底部查看新设计的Footer组件。
        </Paragraph>
        
        <div style={{ backgroundColor: '#2a2a2a', padding: '20px', borderRadius: '8px' }}>
          <Title level={4} style={{ color: '#fff' }}>Footer 组件特性：</Title>
          <ul style={{ color: '#ccc', lineHeight: '1.8' }}>
            <li>高度调整为260px</li>
            <li>深色主题背景色（#1a1a1a）</li>
            <li>左侧：Logo + 平台介绍文本</li>
            <li>右侧上部：社交媒体关注链接</li>
            <li>右侧下部：关于链接</li>
            <li>底部：法律信息和备案信息</li>
            <li>响应式设计支持</li>
            <li>悬停效果和过渡动画</li>
          </ul>
        </div>
        
        <div style={{ backgroundColor: '#2a2a2a', padding: '20px', borderRadius: '8px' }}>
          <Title level={4} style={{ color: '#fff' }}>测试功能：</Title>
          <ul style={{ color: '#ccc', lineHeight: '1.8' }}>
            <li>点击Logo应该跳转到首页</li>
            <li>社交媒体图标应该有悬停效果</li>
            <li>关于链接应该可以点击并跳转</li>
            <li>法律链接应该可以点击并跳转</li>
            <li>在不同屏幕尺寸下应该正确显示</li>
          </ul>
        </div>
        
        <div style={{ height: '200px' }}></div> {/* 占位符，确保页面有足够高度 */}
      </Space>
    </div>
  )
}

export default FooterTest
