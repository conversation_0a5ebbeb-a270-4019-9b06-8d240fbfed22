#!/bin/bash

# AIGC Service Hub - Development Environment Startup Script

echo "🚀 Starting AIGC Service Hub Development Environment..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your actual configuration values"
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p logs
mkdir -p database/seeds
mkdir -p docker/nginx/ssl

# Pull latest images
echo "📦 Pulling latest Docker images..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml pull

# Build and start services
echo "🔨 Building and starting services..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check service health
echo "🏥 Checking service health..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml ps

# Show logs
echo "📋 Showing service logs..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs --tail=50

echo "✅ Development environment is ready!"
echo "🌐 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:3001"
echo "🗄️  Database: localhost:5432"
echo "🔴 Redis: localhost:6379"
echo ""
echo "📊 Optional tools (run with --profile tools):"
echo "   pgAdmin: http://localhost:5050 (<EMAIL> / admin123)"
echo "   Redis Commander: http://localhost:8081"
echo ""
echo "🛑 To stop: docker-compose -f docker-compose.yml -f docker-compose.dev.yml down"
