version: '3.8'

services:
  # Production Database
  postgres:
    environment:
      POSTGRES_DB: aigc_service_hub_prod
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Production Redis
  redis:
    volumes:
      - redis_prod_data:/data
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Production Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    environment:
      NODE_ENV: production
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: aigc_service_hub_prod
      DB_USER: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET}
      PAYPAL_CLIENT_ID: ${PAYPAL_CLIENT_ID}
      PAYP<PERSON>_CLIENT_SECRET: ${PAYPAL_CLIENT_SECRET}
      PAYPAL_MODE: live
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_REGION: us-west-2
      AWS_S3_BUCKET: ${AWS_S3_BUCKET}
      LOG_LEVEL: info
    volumes:
      - ./logs:/app/logs
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    restart: always
    command: npm start

  # Production Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    environment:
      NODE_ENV: production
      VITE_API_BASE_URL: ${API_BASE_URL}
      VITE_PAYPAL_CLIENT_ID: ${PAYPAL_CLIENT_ID}
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    restart: always

  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    container_name: aigc-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    networks:
      - aigc-network
    depends_on:
      - frontend
      - backend
    restart: always
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

volumes:
  postgres_prod_data:
    driver: local
  redis_prod_data:
    driver: local
