import React from 'react'
import { Typography, Space, Card, Row, Col, Avatar } from 'antd'
import { TeamOutlined, RocketOutlined, BulbOutlined, SafetyCertificateOutlined } from '@ant-design/icons'

const { Title, Paragraph, Text } = Typography

const About: React.FC = () => {
  return (
    <div style={{ padding: '24px', color: '#fff', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2} style={{ color: '#fff', marginBottom: '24px' }}>关于 AIGC Service Hub</Title>
      
      <Paragraph style={{ fontSize: '16px', color: '#ccc', marginBottom: '32px' }}>
        AIGC Service Hub 是一个致力于AI资源交易创收的创新平台，旨在为全球的个人创作者和企业创作者提供高效的资源共享与交易渠道。
        平台依托完善的运营管理机制，确保交易的高效性与公平性，助力创作者实现增收盈利，同时推动AI技术的创新与广泛应用。
      </Paragraph>
      
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={12} md={6}>
          <Card 
            style={{ 
              backgroundColor: '#2a2a2a', 
              borderColor: '#444',
              height: '100%'
            }}
            hoverable
          >
            <Space direction="vertical" align="center" style={{ width: '100%', textAlign: 'center' }}>
              <Avatar 
                icon={<TeamOutlined />} 
                style={{ 
                  backgroundColor: '#8B5CF6', 
                  width: '60px', 
                  height: '60px',
                  fontSize: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }} 
              />
              <Title level={4} style={{ color: '#fff', marginTop: '16px' }}>我们的团队</Title>
              <Paragraph style={{ color: '#bbb' }}>
                由AI领域专家、工程师和设计师组成的专业团队，致力于为创作者提供最佳平台体验。
              </Paragraph>
            </Space>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card 
            style={{ 
              backgroundColor: '#2a2a2a', 
              borderColor: '#444',
              height: '100%'
            }}
            hoverable
          >
            <Space direction="vertical" align="center" style={{ width: '100%', textAlign: 'center' }}>
              <Avatar 
                icon={<RocketOutlined />} 
                style={{ 
                  backgroundColor: '#8B5CF6', 
                  width: '60px', 
                  height: '60px',
                  fontSize: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }} 
              />
              <Title level={4} style={{ color: '#fff', marginTop: '16px' }}>我们的使命</Title>
              <Paragraph style={{ color: '#bbb' }}>
                连接全球AI创作者，促进资源共享，推动AI技术创新与应用，实现创作者价值最大化。
              </Paragraph>
            </Space>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card 
            style={{ 
              backgroundColor: '#2a2a2a', 
              borderColor: '#444',
              height: '100%'
            }}
            hoverable
          >
            <Space direction="vertical" align="center" style={{ width: '100%', textAlign: 'center' }}>
              <Avatar 
                icon={<BulbOutlined />} 
                style={{ 
                  backgroundColor: '#8B5CF6', 
                  width: '60px', 
                  height: '60px',
                  fontSize: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }} 
              />
              <Title level={4} style={{ color: '#fff', marginTop: '16px' }}>我们的愿景</Title>
              <Paragraph style={{ color: '#bbb' }}>
                成为全球领先的AI资源交易平台，引领AI创新潮流，为创作者创造更多价值和机会。
              </Paragraph>
            </Space>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card 
            style={{ 
              backgroundColor: '#2a2a2a', 
              borderColor: '#444',
              height: '100%'
            }}
            hoverable
          >
            <Space direction="vertical" align="center" style={{ width: '100%', textAlign: 'center' }}>
              <Avatar 
                icon={<SafetyCertificateOutlined />} 
                style={{ 
                  backgroundColor: '#8B5CF6', 
                  width: '60px', 
                  height: '60px',
                  fontSize: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }} 
              />
              <Title level={4} style={{ color: '#fff', marginTop: '16px' }}>我们的价值观</Title>
              <Paragraph style={{ color: '#bbb' }}>
                创新、诚信、共享、合作，打造公平透明的AI资源交易生态系统。
              </Paragraph>
            </Space>
          </Card>
        </Col>
      </Row>
      
      <div style={{ marginTop: '48px', textAlign: 'center' }}>
        <Title level={3} style={{ color: '#fff' }}>加入我们，共同创造AI未来</Title>
        <Paragraph style={{ color: '#ccc', fontSize: '16px' }}>
          无论您是AI创作者、开发者、设计师还是AI爱好者，都可以在AIGC Service Hub找到属于您的机会和价值。
        </Paragraph>
      </div>
    </div>
  )
}

export default About
