import React from 'react'
import { Modal, Button, Space, Typography } from 'antd'
import {
  ExclamationCircleOutlined,
  CheckOutlined,
  DeleteOutlined,
  WarningOutlined
} from '@ant-design/icons'

const { Text } = Typography

interface ConfirmDialogProps {
  open: boolean
  title: string
  content: string | React.ReactNode
  type?: 'warning' | 'danger' | 'info' | 'success'
  confirmText?: string
  cancelText?: string
  onConfirm: () => void | Promise<void>
  onCancel: () => void
  loading?: boolean
  width?: number
}

export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  open,
  title,
  content,
  type = 'warning',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  loading = false,
  width = 416
}) => {
  const getIcon = () => {
    switch (type) {
      case 'danger':
        return <DeleteOutlined style={{ color: '#ff4d4f', fontSize: '22px' }} />
      case 'warning':
        return <WarningOutlined style={{ color: '#faad14', fontSize: '22px' }} />
      case 'info':
        return <ExclamationCircleOutlined style={{ color: '#1890ff', fontSize: '22px' }} />
      case 'success':
        return <CheckOutlined style={{ color: '#52c41a', fontSize: '22px' }} />
      default:
        return <ExclamationCircleOutlined style={{ color: '#faad14', fontSize: '22px' }} />
    }
  }

  const getConfirmButtonProps = () => {
    switch (type) {
      case 'danger':
        return { danger: true, type: 'primary' as const }
      default:
        return { type: 'primary' as const }
    }
  }

  return (
    <Modal
      open={open}
      title={
        <Space>
          {getIcon()}
          <span>{title}</span>
        </Space>
      }
      width={width}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel} disabled={loading}>
          {cancelText}
        </Button>,
        <Button
          key="confirm"
          {...getConfirmButtonProps()}
          loading={loading}
          onClick={onConfirm}
        >
          {confirmText}
        </Button>
      ]}
      maskClosable={!loading}
      closable={!loading}
    >
      <div className="py-4">
        {typeof content === 'string' ? (
          <Text>{content}</Text>
        ) : (
          content
        )}
      </div>
    </Modal>
  )
}

// Utility function to show confirm dialogs
export const showConfirm = (props: Omit<ConfirmDialogProps, 'open'>) => {
  return new Promise<boolean>((resolve) => {
    let modalInstance: any

    const handleConfirm = async () => {
      try {
        await props.onConfirm()
        modalInstance.destroy()
        resolve(true)
      } catch (error) {
        console.error('Confirm action failed:', error)
        // Don't close modal on error, let user try again
      }
    }

    const handleCancel = () => {
      props.onCancel()
      modalInstance.destroy()
      resolve(false)
    }

    modalInstance = Modal.confirm({
      title: (
        <Space>
          {props.type === 'danger' && <DeleteOutlined style={{ color: '#ff4d4f' }} />}
          {props.type === 'warning' && <WarningOutlined style={{ color: '#faad14' }} />}
          {props.type === 'info' && <ExclamationCircleOutlined style={{ color: '#1890ff' }} />}
          {props.type === 'success' && <CheckOutlined style={{ color: '#52c41a' }} />}
          {!props.type && <ExclamationCircleOutlined style={{ color: '#faad14' }} />}
          <span>{props.title}</span>
        </Space>
      ),
      content: props.content,
      okText: props.confirmText || 'Confirm',
      cancelText: props.cancelText || 'Cancel',
      onOk: handleConfirm,
      onCancel: handleCancel,
      okButtonProps: props.type === 'danger' ? { danger: true } : {},
      width: props.width || 416,
    })
  })
}
