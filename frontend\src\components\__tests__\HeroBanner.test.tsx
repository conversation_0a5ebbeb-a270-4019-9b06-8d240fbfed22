import { describe, it, expect, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { HeroBanner } from '../HeroBanner'
import { render } from '@/test/test-utils'

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => vi.fn()
  }
})

describe('HeroBanner', () => {
  it('renders hero banner container', () => {
    render(<HeroBanner />)
    
    const container = screen.getByText('英雄榜').closest('div')
    expect(container).toBeInTheDocument()
  })

  it('displays hero ranking title', () => {
    render(<HeroBanner />)
    
    expect(screen.getByText('英雄榜')).toBeInTheDocument()
    expect(screen.getByText('排名基于下载量、作品数、点赞数的综合算法，每小时更新')).toBeInTheDocument()
  })

  it('renders carousel component', () => {
    render(<HeroBanner />)
    
    // Carousel should be present
    const carousel = document.querySelector('.ant-carousel')
    expect(carousel).toBeInTheDocument()
  })

  it('displays banner slides with correct content', () => {
    render(<HeroBanner />)
    
    // Check for banner slide content
    expect(screen.getByText('AI创作新纪元')).toBeInTheDocument()
    expect(screen.getByText('发现最优质的AI资源')).toBeInTheDocument()
  })

  it('shows loading state initially', () => {
    render(<HeroBanner />)
    
    expect(screen.getByText('计算排名中...')).toBeInTheDocument()
  })

  it('displays top creators after loading', async () => {
    render(<HeroBanner />)
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('计算排名中...')).not.toBeInTheDocument()
    }, { timeout: 3000 })
    
    // Check for creator names
    expect(screen.getByText('AI大师')).toBeInTheDocument()
    expect(screen.getByText('创意工坊')).toBeInTheDocument()
    expect(screen.getByText('模型专家')).toBeInTheDocument()
  })

  it('displays creator rankings with medals', async () => {
    render(<HeroBanner />)
    
    await waitFor(() => {
      expect(screen.queryByText('计算排名中...')).not.toBeInTheDocument()
    }, { timeout: 3000 })
    
    // Check for medal emojis
    const medals = document.querySelectorAll('.hero-creator-card')
    expect(medals.length).toBeGreaterThan(0)
  })

  it('shows creator statistics', async () => {
    render(<HeroBanner />)
    
    await waitFor(() => {
      expect(screen.queryByText('计算排名中...')).not.toBeInTheDocument()
    }, { timeout: 3000 })
    
    // Check for statistics (downloads, works, likes)
    expect(screen.getByText('15.4k')).toBeInTheDocument() // downloads
    expect(screen.getByText('89')).toBeInTheDocument() // works
    expect(screen.getByText('3.2k')).toBeInTheDocument() // likes
  })

  it('handles creator card clicks', async () => {
    const mockNavigate = vi.fn()
    vi.mocked(require('react-router-dom').useNavigate).mockReturnValue(mockNavigate)
    
    render(<HeroBanner />)
    
    await waitFor(() => {
      expect(screen.queryByText('计算排名中...')).not.toBeInTheDocument()
    }, { timeout: 3000 })
    
    const creatorCard = screen.getByText('AI大师').closest('.hero-creator-card')
    if (creatorCard) {
      fireEvent.click(creatorCard)
      expect(mockNavigate).toHaveBeenCalledWith('/users/ranking')
    }
  })

  it('applies hover effects to creator cards', async () => {
    render(<HeroBanner />)
    
    await waitFor(() => {
      expect(screen.queryByText('计算排名中...')).not.toBeInTheDocument()
    }, { timeout: 3000 })
    
    const creatorCard = screen.getByText('AI大师').closest('.hero-creator-card')
    if (creatorCard) {
      fireEvent.mouseEnter(creatorCard)
      expect(creatorCard).toHaveStyle('transform: translateY(-4px)')
      
      fireEvent.mouseLeave(creatorCard)
      expect(creatorCard).toHaveStyle('transform: translateY(0)')
    }
  })

  it('has correct container dimensions', () => {
    render(<HeroBanner />)
    
    const container = screen.getByText('英雄榜').closest('div')?.parentElement
    expect(container).toHaveStyle('height: 350px')
    expect(container).toHaveStyle('background-color: #1a1a1a')
  })

  it('displays refresh time', () => {
    render(<HeroBanner />)
    
    // Should show last update time
    const timeElement = screen.getByText(/更新于/)
    expect(timeElement).toBeInTheDocument()
  })

  it('shows trend indicators for creators', async () => {
    render(<HeroBanner />)
    
    await waitFor(() => {
      expect(screen.queryByText('计算排名中...')).not.toBeInTheDocument()
    }, { timeout: 3000 })
    
    // Check for trend arrows (up/down indicators)
    const trendIndicators = document.querySelectorAll('.anticon-arrow-up, .anticon-arrow-down')
    expect(trendIndicators.length).toBeGreaterThan(0)
  })
})
