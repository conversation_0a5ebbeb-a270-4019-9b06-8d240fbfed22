/* Footer Component Responsive Styles */

.footer {
  background-color: #1a1a1a;
  color: #fff;
  padding: 40px 0 20px 0;
  height: 260px;
  display: flex;
  flex-direction: column;
}

.footerContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  width: 100%;
  flex: 1;
}

.logoSection {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.logo {
  height: 70px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.logo:hover {
  transform: scale(1.05);
}

.description {
  color: #888;
  font-size: 13px;
  line-height: 1.5;
  display: block;
  max-width: 400px;
}

.rightSection {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.sectionTitle {
  color: #fff;
  font-size: 14px;
  display: block;
  margin-bottom: 12px;
  font-weight: 600;
}

.socialLink {
  color: #888;
  font-size: 20px;
  transition: all 0.2s ease;
  display: inline-block;
}

.socialLink:hover {
  color: #8B5CF6;
  transform: translateY(-2px);
}

.aboutLink {
  color: #888;
  font-size: 13px;
  cursor: pointer;
  transition: color 0.2s ease;
  text-decoration: none;
}

.aboutLink:hover {
  color: #8B5CF6;
}

.legalSection {
  margin-top: auto;
}

.divider {
  border-color: #333;
  margin: 20px 0 16px 0;
}

.legalLink {
  color: #666;
  font-size: 11px;
  cursor: pointer;
  transition: color 0.2s ease;
  text-decoration: none;
}

.legalLink:hover {
  color: #8B5CF6;
}

.registrationInfo {
  color: #666;
  font-size: 11px;
  display: block;
  line-height: 1.4;
}

.copyright {
  color: #666;
  font-size: 11px;
}

/* Responsive Design */

/* Tablet Styles */
@media (max-width: 768px) {
  .footer {
    height: auto;
    min-height: 300px;
    padding: 30px 0 20px 0;
  }
  
  .footerContainer {
    padding: 0 16px;
  }
  
  .logo {
    height: 60px;
  }
  
  .description {
    font-size: 12px;
    max-width: 100%;
  }
  
  .rightSection {
    margin-top: 24px;
    height: auto;
  }
  
  .sectionTitle {
    font-size: 13px;
    margin-bottom: 10px;
  }
  
  .socialLink {
    font-size: 18px;
  }
  
  .aboutLink {
    font-size: 12px;
  }
}

/* Mobile Styles */
@media (max-width: 576px) {
  .footer {
    height: auto;
    min-height: 350px;
    padding: 24px 0 16px 0;
  }
  
  .footerContainer {
    padding: 0 12px;
  }
  
  .logo {
    height: 50px;
  }
  
  .description {
    font-size: 11px;
    margin-top: 12px;
  }
  
  .rightSection {
    margin-top: 20px;
  }
  
  .sectionTitle {
    font-size: 12px;
    margin-bottom: 8px;
  }
  
  .socialLink {
    font-size: 16px;
  }
  
  .aboutLink {
    font-size: 11px;
  }
  
  .legalLink {
    font-size: 10px;
  }
  
  .registrationInfo {
    font-size: 10px;
  }
  
  .copyright {
    font-size: 10px;
  }
  
  .divider {
    margin: 16px 0 12px 0;
  }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
  .footer {
    min-height: 400px;
  }
  
  .description {
    font-size: 10px;
  }
  
  .socialLink {
    font-size: 14px;
  }
}
