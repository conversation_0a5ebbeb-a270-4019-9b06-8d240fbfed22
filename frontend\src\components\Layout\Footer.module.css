/* Footer Component Responsive Styles */

.footer {
  background-color: #1a1a1a;
  color: #fff;
  padding: 40px 0 20px 0;
  height: 260px;
  display: flex;
  flex-direction: column;
}

.footerContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  width: 100%;
  flex: 1;
}

/* 主要内容区域 - 使用flexbox布局 */
.mainContent {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  height: 100%;
  position: relative;
}

/* 左侧区域 - Logo和介绍文字 */
.leftSection {
  flex: 1;
  max-width: 60%;
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* 完全左对齐 */
}

.logoContainer {
  margin-bottom: 16px;
  align-self: flex-start; /* Logo完全左对齐 */
}

.logo {
  height: 70px;
  cursor: pointer;
  transition: transform 0.2s ease;
  display: block;
}

.logo:hover {
  transform: scale(1.05);
}

.descriptionContainer {
  align-self: flex-start; /* 介绍文字完全左对齐 */
}

.description {
  color: #888;
  font-size: 13px;
  line-height: 1.5;
  display: block;
  max-width: 400px;
  text-align: left; /* 文字左对齐 */
}

/* 右侧区域 - 社交媒体和关于链接 */
.rightSection {
  flex: 0 0 auto;
  width: 40%;
  max-width: 320px;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 社交媒体区域 - 右对齐，与Logo顶部对齐 */
.socialSection {
  position: absolute;
  top: 0; /* 与Logo顶部对齐 */
  right: 0; /* 完全右对齐 */
  text-align: right;
}

.socialContent {
  display: inline-block;
  text-align: right;
}

.socialIcons {
  display: flex;
  justify-content: flex-end; /* 图标右对齐 */
  gap: 12px;
  margin-top: 8px;
}

/* 关于链接区域 - 与社交媒体左边缘对齐，与介绍文字顶部对齐 */
.aboutSection {
  position: absolute;
  top: 86px; /* 与介绍文字顶部对齐 (Logo高度70px + margin 16px) */
  right: 0; /* 先右对齐，然后通过transform调整到与社交媒体左边缘对齐 */
  transform: translateX(-100%); /* 移动到社交媒体左边缘 */
  text-align: left;
}

.aboutLinks {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.sectionTitle {
  color: #fff;
  font-size: 14px;
  display: block;
  margin-bottom: 12px;
  font-weight: 600;
}

.socialLink {
  color: #888;
  font-size: 20px;
  transition: all 0.2s ease;
  display: inline-block;
}

.socialLink:hover {
  color: #8B5CF6;
  transform: translateY(-2px);
}

.aboutLink {
  color: #888;
  font-size: 13px;
  cursor: pointer;
  transition: color 0.2s ease;
  text-decoration: none;
  display: block;
}

.aboutLink:hover {
  color: #8B5CF6;
}

/* 法律信息区域 - 完全左对齐 */
.legalSection {
  margin-top: auto;
  width: 100%;
  text-align: left; /* 确保所有内容左对齐 */
}

.divider {
  border-color: #333;
  margin: 20px 0 16px 0;
  width: 100%;
  text-align: left; /* 分割线左对齐 */
}

.legalLinksContainer {
  margin-bottom: 12px;
  text-align: left; /* 法律链接左对齐 */
}

.legalLinksSpace {
  justify-content: flex-start !important; /* 强制左对齐 */
}

.legalLink {
  color: #666;
  font-size: 11px;
  cursor: pointer;
  transition: color 0.2s ease;
  text-decoration: none;
}

.legalLink:hover {
  color: #8B5CF6;
}

.registrationContainer {
  margin-bottom: 8px;
  text-align: left; /* 备案信息左对齐 */
}

.registrationInfo {
  color: #666;
  font-size: 11px;
  display: block;
  line-height: 1.4;
  text-align: left; /* 文字左对齐 */
}

.copyrightContainer {
  text-align: left; /* 版权信息左对齐 */
}

.copyright {
  color: #666;
  font-size: 11px;
  text-align: left; /* 文字左对齐 */
}

/* Responsive Design */

/* Tablet Styles */
@media (max-width: 768px) {
  .footer {
    height: auto;
    min-height: 300px;
    padding: 30px 0 20px 0;
  }

  .footerContainer {
    padding: 0 16px;
  }

  .mainContent {
    flex-direction: column;
    align-items: flex-start;
  }

  .leftSection {
    max-width: 100%;
    margin-bottom: 24px;
  }

  .logo {
    height: 60px;
  }

  .description {
    font-size: 12px;
    max-width: 100%;
  }

  .rightSection {
    position: static;
    width: 100%;
    max-width: 100%;
    flex-direction: column;
    align-items: flex-start;
  }

  .socialSection {
    position: static;
    text-align: left;
    margin-bottom: 20px;
  }

  .socialContent {
    text-align: left;
  }

  .socialIcons {
    justify-content: flex-start;
  }

  .aboutSection {
    position: static;
    transform: none;
    text-align: left;
  }

  .sectionTitle {
    font-size: 13px;
    margin-bottom: 10px;
  }

  .socialLink {
    font-size: 18px;
  }

  .aboutLink {
    font-size: 12px;
  }
}

/* Mobile Styles */
@media (max-width: 576px) {
  .footer {
    height: auto;
    min-height: 350px;
    padding: 24px 0 16px 0;
  }

  .footerContainer {
    padding: 0 12px;
  }

  .mainContent {
    flex-direction: column;
  }

  .leftSection {
    margin-bottom: 20px;
  }

  .logo {
    height: 50px;
  }

  .description {
    font-size: 11px;
    margin-top: 12px;
  }

  .rightSection {
    position: static;
    width: 100%;
    margin-top: 0;
  }

  .socialSection {
    margin-bottom: 16px;
  }

  .sectionTitle {
    font-size: 12px;
    margin-bottom: 8px;
  }

  .socialLink {
    font-size: 16px;
  }

  .aboutLink {
    font-size: 11px;
  }

  .legalLink {
    font-size: 10px;
  }

  .registrationInfo {
    font-size: 10px;
  }

  .copyright {
    font-size: 10px;
  }

  .divider {
    margin: 16px 0 12px 0;
  }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
  .footer {
    min-height: 400px;
  }

  .description {
    font-size: 10px;
  }

  .socialLink {
    font-size: 14px;
  }

  .socialIcons {
    gap: 8px;
  }
}
