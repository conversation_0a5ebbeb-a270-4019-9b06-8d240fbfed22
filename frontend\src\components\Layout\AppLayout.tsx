import React from 'react'
import { Layout } from 'antd'
import { useLocation } from 'react-router-dom'
import { TopHeader } from './TopHeader'
import { MainNavigation } from './MainNavigation'
import { CategoryBar } from './CategoryBar'
import { StyleBar } from './StyleBar'
import { Footer } from './Footer'

const { Content } = Layout

interface AppLayoutProps {
  children: React.ReactNode
}

export const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const location = useLocation()
  
  // 判断是否显示分类和风格栏
  // 首页路径不显示，其他页面显示
  const shouldShowFilters = location.pathname !== '/' && location.pathname !== '/home'
  
  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* H1 - 页眉区域 */}
      <div style={{ position: 'sticky', top: 0, zIndex: 1000, backgroundColor: '#1a1a1a' }}>
        {/* 1. 页眉 - LOGO 搜索 发布 登录 分享 语言 | 高80 */}
        <TopHeader />
        
        {/* 2. 菜单栏 - 首页、微调模型、LoRA、工作流、提示词、工具、挑战、悬赏 | 高50 */}
        <MainNavigation />
        
        {/* 3. 分类栏 - 只在非首页显示 | 高46 */}
        {shouldShowFilters && <CategoryBar />}
        
        {/* 4. 风格栏 - 只在非首页显示 | 高40 */}
        {shouldShowFilters && <StyleBar />}
      </div>

      <Content style={{ backgroundColor: '#1a1a1a' }}>
        {children}
      </Content>

      {/* H5 - 页脚 | 高220 */}
      <Footer />
    </Layout>
  )
}
