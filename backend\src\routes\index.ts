import { Router } from 'express';

// Import route modules
import authRoutes from './auth';
import userRoutes from './users';
import resourceRoutes from './resources';
import categoryRoutes from './categories';
import transactionRoutes from './transactions';
import reviewRoutes from './reviews';
import searchRoutes from './search';
import collectionRoutes from './collections';
import favoriteRoutes from './favorites';
import earningsRoutes from './earnings';
import uploadRoutes from './upload';
import notificationRoutes from './notifications';
import adminRoutes from './admin';

const router = Router();

// API version and health check
router.get('/', (req, res) => {
  res.json({
    success: true,
    data: {
      message: 'AIGC Service Hub API',
      version: '1.0.0',
      status: 'running',
      timestamp: new Date().toISOString()
    }
  });
});

// Mount route modules
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/resources', resourceRoutes);
router.use('/categories', categoryRoutes);
router.use('/transactions', transactionRoutes);
router.use('/reviews', reviewRoutes);
router.use('/search', searchRoutes);
router.use('/collections', collectionRoutes);
router.use('/favorites', favoriteRoutes);
router.use('/earnings', earningsRoutes);
router.use('/upload', uploadRoutes);
router.use('/notifications', notificationRoutes);
router.use('/admin', adminRoutes);

export default router;
