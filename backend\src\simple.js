const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3001;

app.use(cors());
app.use(express.json());

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'AIGC Service Hub Backend is running!'
  });
});

app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'AIGC Service Hub API is working!',
    timestamp: new Date().toISOString()
  });
});

app.get('/api/resources', (req, res) => {
  res.json({
    success: true,
    data: {
      resources: [
        {
          id: '1',
          title: 'AI智能内容生成器专业版',
          description: '基于先进AI技术的智能内容生成工具，支持多种文本创作场景',
          resourceType: 'fine_tuned_model',
          price: 29.99,
          currency: 'USD',
          thumbnailUrl: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=240&fit=crop',
          tags: ['AI', '机器学习', '自然语言处理', '内容生成'],
          creator: {
            id: 'user1',
            username: 'ai_creator',
            fullName: 'AI创作者',
            email: '<EMAIL>',
            isVerified: true
          },
          status: 'approved',
          isFeatured: true,
          ratingAverage: 4.8,
          ratingCount: 15,
          downloadCount: 256,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ],
      pagination: {
        page: 1,
        limit: 20,
        total: 1,
        pages: 1
      }
    },
    timestamp: new Date().toISOString()
  });
});

app.get('/api/categories', (req, res) => {
  res.json({
    success: true,
    data: {
      categories: [
        {
          id: 'cat1',
          name: 'AI模型',
          description: '微调AI模型和适配器',
          slug: 'ai-models',
          isActive: true
        }
      ]
    },
    timestamp: new Date().toISOString()
  });
});

app.listen(PORT, () => {
  console.log(`🚀 AIGC Service Hub Backend running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log(`🧪 Test API: http://localhost:${PORT}/api/test`);
  console.log(`📦 Resources API: http://localhost:${PORT}/api/resources`);
});
