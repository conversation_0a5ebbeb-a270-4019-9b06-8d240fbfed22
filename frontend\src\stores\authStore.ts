import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User } from '@/types'
import { authApi } from '@/services/api'

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

interface AuthActions {
  login: (email: string, password: string) => Promise<void>
  register: (data: {
    email: string
    password: string
    username: string
    fullName: string
    userType?: string
  }) => Promise<void>
  logout: () => void
  clearError: () => void
  setLoading: (loading: boolean) => void
  updateUser: (user: Partial<User>) => void
}

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null })
          
          const response = await authApi.login({ email, password })
          
          if (response.data.success && response.data.data) {
            const { user, token } = response.data.data
            
            // Store token in localStorage
            localStorage.setItem('authToken', token)
            
            set({
              user,
              token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
          } else {
            throw new Error(response.data.error?.message || 'Login failed')
          }
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.response?.data?.error?.message || error.message || 'Login failed',
          })
          throw error
        }
      },

      register: async (data) => {
        try {
          set({ isLoading: true, error: null })
          
          const response = await authApi.register(data)
          
          if (response.data.success && response.data.data) {
            const { user, token } = response.data.data
            
            // Store token in localStorage
            localStorage.setItem('authToken', token)
            
            set({
              user,
              token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
          } else {
            throw new Error(response.data.error?.message || 'Registration failed')
          }
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.response?.data?.error?.message || error.message || 'Registration failed',
          })
          throw error
        }
      },

      logout: () => {
        // Remove token from localStorage
        localStorage.removeItem('authToken')
        
        // Call logout API (optional, for token blacklisting)
        authApi.logout().catch(() => {
          // Ignore errors on logout
        })
        
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null,
        })
      },

      clearError: () => {
        set({ error: null })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      updateUser: (userData: Partial<User>) => {
        const { user } = get()
        if (user) {
          set({ user: { ...user, ...userData } })
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
