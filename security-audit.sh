#!/bin/bash

# AIGC Service Hub Security Audit Script
# This script performs comprehensive security checks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# Logging functions
log() {
    echo -e "${GREEN}[PASS] $1${NC}"
    ((PASSED_CHECKS++))
}

warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
    ((WARNING_CHECKS++))
}

error() {
    echo -e "${RED}[FAIL] $1${NC}"
    ((FAILED_CHECKS++))
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

check() {
    ((TOTAL_CHECKS++))
}

# Header
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    AIGC Service Hub Security Audit    ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 1. Check for sensitive files
info "1. Checking for sensitive files..."
check

if [ -f ".env" ]; then
    error "Found .env file in root directory - should be in .gitignore"
else
    log "No .env file found in root directory"
fi

if [ -f "frontend/.env" ]; then
    warn "Found .env file in frontend directory - ensure it doesn't contain secrets"
fi

if [ -f "backend/.env" ]; then
    warn "Found .env file in backend directory - ensure it doesn't contain secrets"
fi

# 2. Check .gitignore
info "2. Checking .gitignore configuration..."
check

if grep -q "\.env" .gitignore 2>/dev/null; then
    log ".env files are properly ignored in .gitignore"
else
    error ".env files are not ignored in .gitignore"
fi

if grep -q "node_modules" .gitignore 2>/dev/null; then
    log "node_modules are properly ignored"
else
    warn "node_modules should be in .gitignore"
fi

# 3. Check package.json for known vulnerabilities
info "3. Checking for known vulnerabilities in dependencies..."
check

if command -v npm &> /dev/null; then
    cd frontend
    if npm audit --audit-level=high 2>/dev/null; then
        log "Frontend dependencies have no high-severity vulnerabilities"
    else
        error "Frontend dependencies have high-severity vulnerabilities"
    fi
    cd ..
    
    cd backend
    if npm audit --audit-level=high 2>/dev/null; then
        log "Backend dependencies have no high-severity vulnerabilities"
    else
        error "Backend dependencies have high-severity vulnerabilities"
    fi
    cd ..
else
    warn "npm not found - skipping dependency vulnerability check"
fi

# 4. Check for hardcoded secrets
info "4. Scanning for hardcoded secrets..."
check

SECRET_PATTERNS=(
    "password\s*=\s*['\"][^'\"]*['\"]"
    "secret\s*=\s*['\"][^'\"]*['\"]"
    "api_key\s*=\s*['\"][^'\"]*['\"]"
    "token\s*=\s*['\"][^'\"]*['\"]"
    "jwt_secret\s*=\s*['\"][^'\"]*['\"]"
)

FOUND_SECRETS=false
for pattern in "${SECRET_PATTERNS[@]}"; do
    if grep -r -i -E "$pattern" --include="*.js" --include="*.ts" --include="*.jsx" --include="*.tsx" . 2>/dev/null | grep -v node_modules | grep -v ".git"; then
        FOUND_SECRETS=true
    fi
done

if [ "$FOUND_SECRETS" = true ]; then
    error "Found potential hardcoded secrets in source code"
else
    log "No hardcoded secrets found in source code"
fi

# 5. Check Docker security
info "5. Checking Docker security configurations..."
check

# Check Dockerfile for security best practices
if [ -f "frontend/Dockerfile" ]; then
    if grep -q "USER" frontend/Dockerfile; then
        log "Frontend Dockerfile uses non-root user"
    else
        warn "Frontend Dockerfile should specify a non-root USER"
    fi
fi

if [ -f "backend/Dockerfile" ]; then
    if grep -q "USER" backend/Dockerfile; then
        log "Backend Dockerfile uses non-root user"
    else
        warn "Backend Dockerfile should specify a non-root USER"
    fi
fi

# 6. Check HTTPS configuration
info "6. Checking HTTPS configuration..."
check

if [ -f "nginx/nginx.conf" ]; then
    if grep -q "ssl" nginx/nginx.conf; then
        log "Nginx configuration includes SSL settings"
    else
        warn "Nginx configuration should include SSL/HTTPS settings"
    fi
else
    warn "Nginx configuration file not found"
fi

# 7. Check CORS configuration
info "7. Checking CORS configuration..."
check

if grep -r "cors" backend/ 2>/dev/null | grep -v node_modules; then
    log "CORS configuration found in backend"
else
    warn "CORS configuration should be properly configured"
fi

# 8. Check for security headers
info "8. Checking security headers configuration..."
check

SECURITY_HEADERS=(
    "X-Content-Type-Options"
    "X-Frame-Options"
    "X-XSS-Protection"
    "Strict-Transport-Security"
    "Content-Security-Policy"
)

for header in "${SECURITY_HEADERS[@]}"; do
    if grep -r "$header" . 2>/dev/null | grep -v node_modules | grep -v ".git"; then
        log "Security header $header is configured"
    else
        warn "Security header $header should be configured"
    fi
done

# 9. Check database security
info "9. Checking database security..."
check

if [ -f "docker-compose.yml" ] || [ -f "docker-compose.prod.yml" ]; then
    if grep -q "POSTGRES_PASSWORD" docker-compose*.yml; then
        warn "Database password should be stored in environment variables or secrets"
    fi
    
    if grep -q "5432:5432" docker-compose*.yml; then
        warn "Database port should not be exposed publicly in production"
    fi
fi

# 10. Check file permissions
info "10. Checking file permissions..."
check

# Check for overly permissive files
if find . -type f -perm 777 2>/dev/null | grep -v node_modules | grep -v ".git"; then
    error "Found files with 777 permissions"
else
    log "No files with overly permissive permissions found"
fi

# Summary
echo ""
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}           Security Audit Summary       ${NC}"
echo -e "${BLUE}========================================${NC}"
echo -e "Total Checks: ${TOTAL_CHECKS}"
echo -e "${GREEN}Passed: ${PASSED_CHECKS}${NC}"
echo -e "${YELLOW}Warnings: ${WARNING_CHECKS}${NC}"
echo -e "${RED}Failed: ${FAILED_CHECKS}${NC}"
echo ""

# Calculate security score
SECURITY_SCORE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
echo -e "Security Score: ${SECURITY_SCORE}%"

if [ $SECURITY_SCORE -ge 80 ]; then
    echo -e "${GREEN}Security status: GOOD${NC}"
elif [ $SECURITY_SCORE -ge 60 ]; then
    echo -e "${YELLOW}Security status: MODERATE${NC}"
else
    echo -e "${RED}Security status: POOR${NC}"
fi

echo ""
echo -e "${BLUE}Recommendations:${NC}"
echo "1. Fix all FAILED checks immediately"
echo "2. Address WARNING items before production deployment"
echo "3. Regularly update dependencies to patch vulnerabilities"
echo "4. Implement proper secret management (AWS Secrets Manager)"
echo "5. Enable security monitoring and logging"
echo "6. Conduct regular security audits"

# Exit with appropriate code
if [ $FAILED_CHECKS -gt 0 ]; then
    exit 1
else
    exit 0
fi
