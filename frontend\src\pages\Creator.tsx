import React from 'react'
import { useParams } from 'react-router-dom'
import { Typography, Avatar, Row, Col, Card, Statistic, Button, Space } from 'antd'
import { UserOutlined, DownloadOutlined, HeartOutlined, EyeOutlined, PlusOutlined } from '@ant-design/icons'

const { Title, Text, Paragraph } = Typography

export const Creator: React.FC = () => {
  const { creatorId } = useParams<{ creatorId: string }>()

  // 模拟创作者数据
  const creatorData = {
    'ai-master': {
      id: 'ai-master',
      name: 'AI大师',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=120&h=120&fit=crop&crop=face',
      bio: '专注于AI写真和人像生成技术，拥有5年AI模型训练经验。致力于为用户提供高质量的AI生成模型。',
      joinDate: '2023年3月',
      location: '北京',
      totalWorks: 89,
      totalDownloads: 15400,
      totalLikes: 3200,
      followers: 1250
    },
    'creative-workshop': {
      id: 'creative-workshop',
      name: '创意工坊',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=120&h=120&fit=crop&crop=face',
      bio: '专业的AI工作流设计团队，专注于商业应用场景的AI解决方案。帮助企业快速部署AI应用。',
      joinDate: '2023年1月',
      location: '上海',
      totalWorks: 67,
      totalDownloads: 12300,
      totalLikes: 2900,
      followers: 980
    },
    'copywriter-expert': {
      id: 'copywriter-expert',
      name: '文案专家',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=120&h=120&fit=crop&crop=face',
      bio: '资深文案策划师，结合AI技术开发智能文案生成工具。专注于营销文案和创意写作。',
      joinDate: '2023年6月',
      location: '深圳',
      totalWorks: 45,
      totalDownloads: 9900,
      totalLikes: 2200,
      followers: 650
    }
  }

  const creator = creatorData[creatorId as keyof typeof creatorData]

  if (!creator) {
    return (
      <div style={{ 
        padding: '48px 24px', 
        textAlign: 'center', 
        backgroundColor: '#1a1a1a',
        minHeight: '60vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div>
          <UserOutlined style={{ fontSize: '64px', color: '#666', marginBottom: '16px' }} />
          <Title level={3} style={{ color: '#fff' }}>创作者不存在</Title>
          <Text style={{ color: '#888' }}>抱歉，找不到该创作者的信息</Text>
        </div>
      </div>
    )
  }

  return (
    <div style={{ backgroundColor: '#1a1a1a', minHeight: '100vh', padding: '24px' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* 创作者信息头部 */}
        <Card 
          style={{ 
            backgroundColor: '#2a2a2a', 
            borderColor: '#3a3a3a',
            marginBottom: '24px'
          }}
        >
          <Row gutter={[24, 24]} align="middle">
            <Col xs={24} sm={6} md={4}>
              <Avatar 
                src={creator.avatar} 
                size={120}
                style={{ display: 'block', margin: '0 auto' }}
              />
            </Col>
            <Col xs={24} sm={18} md={14}>
              <Title level={2} style={{ color: '#fff', marginBottom: '8px' }}>
                {creator.name}
              </Title>
              <Paragraph style={{ color: '#ccc', marginBottom: '16px' }}>
                {creator.bio}
              </Paragraph>
              <Space size="large" wrap>
                <Text style={{ color: '#888' }}>
                  📍 {creator.location}
                </Text>
                <Text style={{ color: '#888' }}>
                  📅 加入于 {creator.joinDate}
                </Text>
              </Space>
            </Col>
            <Col xs={24} sm={24} md={6}>
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  size="large"
                  block
                  style={{
                    background: 'linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%)',
                    borderColor: '#8B5CF6'
                  }}
                >
                  关注
                </Button>
                <Button 
                  size="large"
                  block
                  style={{
                    backgroundColor: '#3a3a3a',
                    borderColor: '#4a4a4a',
                    color: '#fff'
                  }}
                >
                  私信
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 统计数据 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={12} sm={6}>
            <Card 
              style={{ 
                backgroundColor: '#2a2a2a', 
                borderColor: '#3a3a3a',
                textAlign: 'center'
              }}
            >
              <Statistic
                title={<span style={{ color: '#888' }}>作品数量</span>}
                value={creator.totalWorks}
                valueStyle={{ color: '#fff' }}
                prefix={<EyeOutlined style={{ color: '#8B5CF6' }} />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card 
              style={{ 
                backgroundColor: '#2a2a2a', 
                borderColor: '#3a3a3a',
                textAlign: 'center'
              }}
            >
              <Statistic
                title={<span style={{ color: '#888' }}>总下载量</span>}
                value={creator.totalDownloads}
                valueStyle={{ color: '#fff' }}
                prefix={<DownloadOutlined style={{ color: '#10B981' }} />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card 
              style={{ 
                backgroundColor: '#2a2a2a', 
                borderColor: '#3a3a3a',
                textAlign: 'center'
              }}
            >
              <Statistic
                title={<span style={{ color: '#888' }}>总点赞数</span>}
                value={creator.totalLikes}
                valueStyle={{ color: '#fff' }}
                prefix={<HeartOutlined style={{ color: '#F59E0B' }} />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card 
              style={{ 
                backgroundColor: '#2a2a2a', 
                borderColor: '#3a3a3a',
                textAlign: 'center'
              }}
            >
              <Statistic
                title={<span style={{ color: '#888' }}>粉丝数量</span>}
                value={creator.followers}
                valueStyle={{ color: '#fff' }}
                prefix={<UserOutlined style={{ color: '#8B5CF6' }} />}
              />
            </Card>
          </Col>
        </Row>

        {/* 作品展示区域 */}
        <Card 
          style={{ 
            backgroundColor: '#2a2a2a', 
            borderColor: '#3a3a3a'
          }}
          title={
            <Title level={4} style={{ color: '#fff', margin: 0 }}>
              最新作品
            </Title>
          }
        >
          <div style={{ 
            textAlign: 'center', 
            padding: '48px 24px',
            color: '#888'
          }}>
            <EyeOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
            <div>作品列表功能开发中...</div>
          </div>
        </Card>
      </div>
    </div>
  )
}
