import { useAuthStore } from '@/stores/authStore'

export const useAuth = () => {
  const {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    logout,
    clearError,
    setLoading,
    updateUser,
  } = useAuthStore()

  return {
    // State
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    
    // Actions
    login,
    register,
    logout,
    clearError,
    setLoading,
    updateUser,
    
    // Computed
    isAdmin: user?.userType === 'admin',
    isEnterprise: user?.userType === 'enterprise',
    isVerified: user?.isVerified || false,
  }
}
