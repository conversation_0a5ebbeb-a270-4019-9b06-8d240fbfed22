import React from 'react'
import { <PERSON> } from 'react-router-dom'
import {
  Card,
  Tag,
  Button,
  Avatar,
  Rate,
  Typography,
  Space,
  Tooltip,
  Badge
} from 'antd'
import {
  DownloadOutlined,
  ShoppingCartOutlined,
  StarFilled,
  VerifiedOutlined,
  CrownOutlined
} from '@ant-design/icons'
import { Resource } from '@/types'

const { Text, Title, Paragraph } = Typography
const { Meta } = Card

interface ResourceCardProps {
  resource: Resource
  onPurchase?: (resourceId: string) => void
  showPurchaseButton?: boolean
}

export const ResourceCard: React.FC<ResourceCardProps> = ({
  resource,
  onPurchase,
  showPurchaseButton = true
}) => {
  const handlePurchase = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (onPurchase) {
      onPurchase(resource.id)
    }
  }

  const formatPrice = (price: number, currency: string) => {
    if (price === 0) return '免费'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(price)
  }

  const getResourceTypeColor = (type: string) => {
    const colors = {
      'fine_tuned_model': 'blue',
      'lora': 'green',
      'workflow': 'purple',
      'prompt': 'orange',
      'tool': 'red'
    }
    return colors[type as keyof typeof colors] || 'default'
  }

  const formatResourceType = (type: string) => {
    const types = {
      'fine_tuned_model': '微调模型',
      'lora': 'LoRA适配器',
      'workflow': '工作流',
      'prompt': '提示词',
      'tool': '工具'
    }
    return types[type as keyof typeof types] || type
  }

  const getResourceIcon = (type: string) => {
    const icons = {
      'fine_tuned_model': '🤖',
      'lora': '⚡',
      'workflow': '🔄',
      'prompt': '💬',
      'tool': '🛠️'
    }
    return icons[type as keyof typeof types] || '📦'
  }

  const actions = [
    <Tooltip title="下载次数">
      <Space>
        <DownloadOutlined />
        <Text type="secondary">{resource.downloadCount}</Text>
      </Space>
    </Tooltip>,
    <Tooltip title="评分">
      <Space>
        <Rate disabled defaultValue={resource.ratingAverage} count={1} />
        <Text type="secondary">
          {resource.ratingAverage.toFixed(1)} ({resource.ratingCount})
        </Text>
      </Space>
    </Tooltip>
  ]

  return (
    <Link to={`/resources/${resource.id}`}>
      <Badge.Ribbon
        text={resource.isFeatured ? "精选" : undefined}
        color="gold"
        style={{ display: resource.isFeatured ? 'block' : 'none' }}
      >
        <Card
          hoverable
          className="h-full"
          cover={
            <div className="relative h-48 bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
              {resource.thumbnailUrl ? (
                <img
                  alt={resource.title}
                  src={resource.thumbnailUrl}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="text-6xl opacity-60">
                  {getResourceIcon(resource.resourceType)}
                </div>
              )}
              <div className="absolute top-2 right-2">
                <Tag color={getResourceTypeColor(resource.resourceType)}>
                  {formatResourceType(resource.resourceType)}
                </Tag>
              </div>
            </div>
          }
          actions={showPurchaseButton ? [
            <Button
              type={resource.price === 0 ? "default" : "primary"}
              icon={resource.price === 0 ? <DownloadOutlined /> : <ShoppingCartOutlined />}
              onClick={handlePurchase}
              block
            >
              {resource.price === 0 ? '下载' : formatPrice(resource.price, resource.currency)}
            </Button>
          ] : actions}
        >
          <Meta
            title={
              <Space direction="vertical" size="small" className="w-full">
                <Title level={5} ellipsis={{ rows: 2 }} className="!mb-0">
                  {resource.title}
                </Title>
                <Space size="small">
                  <Avatar size="small" src={resource.creator.avatarUrl}>
                    {resource.creator.username.charAt(0).toUpperCase()}
                  </Avatar>
                  <Text type="secondary">
                    {resource.creator.username}
                  </Text>
                  {resource.creator.isVerified && (
                    <Tooltip title="认证创作者">
                      <VerifiedOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  )}
                </Space>
              </Space>
            }
            description={
              <Space direction="vertical" size="small" className="w-full">
                <Paragraph
                  ellipsis={{ rows: 3 }}
                  type="secondary"
                  className="!mb-2"
                >
                  {resource.description}
                </Paragraph>

                {resource.tags && resource.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {resource.tags.slice(0, 3).map((tag, index) => (
                      <Tag key={index} size="small">
                        {tag}
                      </Tag>
                    ))}
                    {resource.tags.length > 3 && (
                      <Tag size="small">
                        +{resource.tags.length - 3}
                      </Tag>
                    )}
                  </div>
                )}

                {!showPurchaseButton && (
                  <div className="flex justify-between items-center mt-2">
                    <Space>
                      <DownloadOutlined />
                      <Text type="secondary">{resource.downloadCount}</Text>
                    </Space>
                    <Space>
                      <StarFilled style={{ color: '#faad14' }} />
                      <Text type="secondary">
                        {resource.ratingAverage.toFixed(1)} ({resource.ratingCount})
                      </Text>
                    </Space>
                    <Text strong style={{ color: '#52c41a' }}>
                      {formatPrice(resource.price, resource.currency)}
                    </Text>
                  </div>
                )}
              </Space>
            }
          />
        </Card>
      </Badge.Ribbon>
    </Link>
  )
}
