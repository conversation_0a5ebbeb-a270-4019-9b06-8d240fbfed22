const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Basic middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'AIGC Service Hub Backend is running!'
  });
});

// Test API endpoint
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'AIGC Service Hub API is working!',
    timestamp: new Date().toISOString()
  });
});

// Mock resources endpoint
app.get('/api/resources', (req, res) => {
  res.json({
    success: true,
    data: {
      resources: [
        {
          id: '1',
          title: 'AI智能内容生成器专业版',
          description: '基于先进AI技术的智能内容生成工具，支持多种文本创作场景',
          resourceType: 'fine_tuned_model',
          price: 29.99,
          currency: 'USD',
          thumbnailUrl: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=240&fit=crop',
          tags: ['AI', '机器学习', '自然语言处理', '内容生成'],
          creator: {
            id: 'user1',
            username: 'ai_creator',
            fullName: 'AI创作者',
            email: '<EMAIL>',
            isVerified: true
          },
          status: 'approved',
          isFeatured: true,
          ratingAverage: 4.8,
          ratingCount: 15,
          downloadCount: 256,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '2',
          title: 'ChatGPT提示词优化工具',
          description: '专业的提示词工程工具，帮助您创建更有效的AI对话',
          resourceType: 'tool',
          price: 0,
          currency: 'USD',
          thumbnailUrl: 'https://images.unsplash.com/photo-1676299081847-824916de030a?w=400&h=240&fit=crop',
          tags: ['提示词', 'ChatGPT', '工具', '免费'],
          creator: {
            id: 'user2',
            username: 'prompt_master',
            fullName: '提示词大师',
            email: '<EMAIL>',
            isVerified: true
          },
          status: 'approved',
          isFeatured: false,
          ratingAverage: 4.5,
          ratingCount: 8,
          downloadCount: 89,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '3',
          title: 'Stable Diffusion LoRA适配器',
          description: '高质量的图像生成LoRA模型，专门用于艺术风格转换',
          resourceType: 'lora',
          price: 15.99,
          currency: 'USD',
          thumbnailUrl: 'https://images.unsplash.com/photo-1686191128892-c0c8b8e5e3b8?w=400&h=240&fit=crop',
          tags: ['LoRA', 'Stable Diffusion', '图像生成', '艺术'],
          creator: {
            id: 'user3',
            username: 'art_ai',
            fullName: '艺术AI',
            email: '<EMAIL>',
            isVerified: false
          },
          status: 'approved',
          isFeatured: true,
          ratingAverage: 4.9,
          ratingCount: 23,
          downloadCount: 178,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ],
      pagination: {
        page: 1,
        limit: 20,
        total: 3,
        pages: 1
      }
    },
    timestamp: new Date().toISOString()
  });
});

app.get('/api/categories', (req, res) => {
  res.json({
    success: true,
    data: {
      categories: [
        {
          id: 'cat1',
          name: 'AI模型',
          description: '微调AI模型和适配器',
          slug: 'ai-models',
          isActive: true
        },
        {
          id: 'cat2',
          name: '工作流',
          description: 'AI自动化工作流程',
          slug: 'workflows',
          isActive: true
        },
        {
          id: 'cat3',
          name: '提示词',
          description: '优化的AI提示词模板',
          slug: 'prompts',
          isActive: true
        },
        {
          id: 'cat4',
          name: '工具',
          description: 'AI开发和应用工具',
          slug: 'tools',
          isActive: true
        }
      ]
    },
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 AIGC Service Hub Backend running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log(`🧪 Test API: http://localhost:${PORT}/api/test`);
  console.log(`📦 Resources API: http://localhost:${PORT}/api/resources`);
});
