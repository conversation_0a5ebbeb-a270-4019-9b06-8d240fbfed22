version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: aigc-postgres
    environment:
      POSTGRES_DB: aigc_service_hub
      POSTGRES_USER: aigc_user
      POSTGRES_PASSWORD: aigc_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - aigc-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: aigc-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - aigc-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Backend API Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: aigc-backend
    environment:
      NODE_ENV: development
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: aigc_service_hub
      DB_USER: aigc_user
      DB_PASSWORD: aigc_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: your-jwt-secret-key
      PAYPAL_CLIENT_ID: your-paypal-client-id
      PAYPAL_CLIENT_SECRET: your-paypal-client-secret
      AWS_ACCESS_KEY_ID: your-aws-access-key
      AWS_SECRET_ACCESS_KEY: your-aws-secret-key
      AWS_REGION: us-west-2
      AWS_S3_BUCKET: aigc-service-hub-files
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - aigc-network
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    command: npm run dev

  # Frontend React Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: aigc-frontend
    environment:
      NODE_ENV: development
      VITE_API_BASE_URL: http://localhost:3001/api
      VITE_PAYPAL_CLIENT_ID: your-paypal-client-id
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - aigc-network
    depends_on:
      - backend
    restart: unless-stopped
    command: npm run dev

  # Nginx Reverse Proxy (for production)
  nginx:
    image: nginx:alpine
    container_name: aigc-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    networks:
      - aigc-network
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  aigc-network:
    driver: bridge
