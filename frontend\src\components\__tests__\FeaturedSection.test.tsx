import { describe, it, expect, vi } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import { FeaturedSection } from '../FeaturedSection'
import { render } from '@/test/test-utils'

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => vi.fn()
  }
})

describe('FeaturedSection', () => {
  it('renders section title', () => {
    render(<FeaturedSection />)
    
    expect(screen.getByText('精选作品')).toBeInTheDocument()
    expect(screen.getByText('管理员精心挑选的优质AI资源')).toBeInTheDocument()
  })

  it('displays featured works cards', () => {
    render(<FeaturedSection />)
    
    // Check for featured work titles
    expect(screen.getByText('AI写真大师模型')).toBeInTheDocument()
    expect(screen.getByText('创意工作流集合')).toBeInTheDocument()
    expect(screen.getByText('智能提示词生成器')).toBeInTheDocument()
  })

  it('shows creator information', () => {
    render(<FeaturedSection />)
    
    // Check for creator names
    expect(screen.getByText('AI大师')).toBeInTheDocument()
    expect(screen.getByText('创意工坊')).toBeInTheDocument()
    expect(screen.getByText('提示词专家')).toBeInTheDocument()
  })

  it('displays resource type tags', () => {
    render(<FeaturedSection />)
    
    // Check for resource type tags
    expect(screen.getByText('LoRA')).toBeInTheDocument()
    expect(screen.getByText('工作流')).toBeInTheDocument()
    expect(screen.getByText('提示词')).toBeInTheDocument()
  })

  it('shows statistics for each work', () => {
    render(<FeaturedSection />)
    
    // Check for download counts
    expect(screen.getByText('1520')).toBeInTheDocument() // downloads
    expect(screen.getByText('890')).toBeInTheDocument() // downloads
    expect(screen.getByText('2100')).toBeInTheDocument() // downloads
    
    // Check for like counts
    expect(screen.getByText('340')).toBeInTheDocument() // likes
    expect(screen.getByText('156')).toBeInTheDocument() // likes
    expect(screen.getByText('445')).toBeInTheDocument() // likes
  })

  it('handles card click navigation', () => {
    const mockNavigate = vi.fn()
    vi.mocked(require('react-router-dom').useNavigate).mockReturnValue(mockNavigate)
    
    render(<FeaturedSection />)
    
    const firstCard = screen.getByText('AI写真大师模型').closest('.featured-work-card')
    if (firstCard) {
      fireEvent.click(firstCard)
      expect(mockNavigate).toHaveBeenCalledWith('/resource/ai-portrait-master-lora')
    }
  })

  it('handles creator click navigation', () => {
    const mockNavigate = vi.fn()
    vi.mocked(require('react-router-dom').useNavigate).mockReturnValue(mockNavigate)
    
    render(<FeaturedSection />)
    
    const creatorElement = screen.getByText('AI大师')
    fireEvent.click(creatorElement)
    expect(mockNavigate).toHaveBeenCalledWith('/creator/ai-master')
  })

  it('applies hover effects to cards', () => {
    render(<FeaturedSection />)
    
    const firstCard = screen.getByText('AI写真大师模型').closest('.featured-work-card')
    if (firstCard) {
      fireEvent.mouseEnter(firstCard)
      expect(firstCard).toHaveStyle('transform: translateY(-2px)')
      
      fireEvent.mouseLeave(firstCard)
      expect(firstCard).toHaveStyle('transform: translateY(0)')
    }
  })

  it('displays featured labels', () => {
    render(<FeaturedSection />)
    
    // Check for "精选" labels
    const featuredLabels = screen.getAllByText('精选')
    expect(featuredLabels.length).toBeGreaterThan(0)
  })

  it('has correct responsive grid layout', () => {
    render(<FeaturedSection />)
    
    // Check for Ant Design grid columns
    const gridCols = document.querySelectorAll('.ant-col-xs-24.ant-col-sm-24.ant-col-md-8.ant-col-lg-8.ant-col-xl-8')
    expect(gridCols.length).toBe(3) // 3 featured works
  })

  it('displays creator avatars', () => {
    render(<FeaturedSection />)
    
    // Check for avatar images
    const avatars = document.querySelectorAll('.ant-avatar')
    expect(avatars.length).toBe(3) // 3 creators
  })

  it('shows view counts', () => {
    render(<FeaturedSection />)
    
    // Check for view statistics
    expect(screen.getByText('8900')).toBeInTheDocument() // views
    expect(screen.getByText('5600')).toBeInTheDocument() // views
    expect(screen.getByText('12000')).toBeInTheDocument() // views
  })

  it('has proper section styling', () => {
    render(<FeaturedSection />)
    
    const section = screen.getByText('精选作品').closest('div')?.parentElement
    expect(section).toHaveStyle('background-color: #1a1a1a')
    expect(section).toHaveStyle('padding: 24px')
  })

  it('displays star icon in title', () => {
    render(<FeaturedSection />)
    
    const starIcon = document.querySelector('.anticon-star')
    expect(starIcon).toBeInTheDocument()
  })
})
