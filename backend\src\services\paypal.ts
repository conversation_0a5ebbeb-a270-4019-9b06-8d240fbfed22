import paypal from 'paypal-rest-sdk';

// Configure PayPal
paypal.configure({
  mode: process.env.PAYPAL_MODE || 'sandbox', // sandbox or live
  client_id: process.env.PAYPAL_CLIENT_ID || '',
  client_secret: process.env.PAYPAL_CLIENT_SECRET || ''
});

export interface PaymentData {
  amount: number;
  currency: string;
  description: string;
  returnUrl: string;
  cancelUrl: string;
  metadata?: Record<string, any>;
}

export interface PaymentResult {
  paymentId: string;
  approvalUrl: string;
  status: string;
}

export class PayPalService {
  static async createPayment(paymentData: PaymentData): Promise<PaymentResult> {
    return new Promise((resolve, reject) => {
      const payment = {
        intent: 'sale',
        payer: {
          payment_method: 'paypal'
        },
        redirect_urls: {
          return_url: paymentData.returnUrl,
          cancel_url: paymentData.cancelUrl
        },
        transactions: [{
          amount: {
            total: paymentData.amount.toFixed(2),
            currency: paymentData.currency
          },
          description: paymentData.description,
          custom: JSON.stringify(paymentData.metadata || {})
        }]
      };

      paypal.payment.create(payment, (error, payment) => {
        if (error) {
          console.error('PayPal payment creation error:', error);
          reject(new Error('Failed to create PayPal payment'));
        } else {
          const approvalUrl = payment.links?.find(link => link.rel === 'approval_url')?.href;
          
          if (!approvalUrl) {
            reject(new Error('No approval URL found in PayPal response'));
            return;
          }

          resolve({
            paymentId: payment.id!,
            approvalUrl,
            status: payment.state!
          });
        }
      });
    });
  }

  static async executePayment(paymentId: string, payerId: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const executePaymentJson = {
        payer_id: payerId
      };

      paypal.payment.execute(paymentId, executePaymentJson, (error, payment) => {
        if (error) {
          console.error('PayPal payment execution error:', error);
          reject(new Error('Failed to execute PayPal payment'));
        } else {
          resolve(payment);
        }
      });
    });
  }

  static async getPayment(paymentId: string): Promise<any> {
    return new Promise((resolve, reject) => {
      paypal.payment.get(paymentId, (error, payment) => {
        if (error) {
          console.error('PayPal get payment error:', error);
          reject(new Error('Failed to get PayPal payment'));
        } else {
          resolve(payment);
        }
      });
    });
  }

  static async refundSale(saleId: string, amount?: number, currency?: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const refundData: any = {};
      
      if (amount && currency) {
        refundData.amount = {
          total: amount.toFixed(2),
          currency: currency
        };
      }

      paypal.sale.refund(saleId, refundData, (error, refund) => {
        if (error) {
          console.error('PayPal refund error:', error);
          reject(new Error('Failed to process PayPal refund'));
        } else {
          resolve(refund);
        }
      });
    });
  }

  static validateWebhookSignature(headers: any, body: string): boolean {
    try {
      // In a real implementation, you would validate the webhook signature
      // using PayPal's webhook verification API
      // For now, we'll do basic validation
      
      const webhookId = process.env.PAYPAL_WEBHOOK_ID;
      if (!webhookId) {
        console.warn('PayPal webhook ID not configured');
        return false;
      }

      // Basic header validation
      const requiredHeaders = ['paypal-transmission-id', 'paypal-cert-id', 'paypal-transmission-sig'];
      for (const header of requiredHeaders) {
        if (!headers[header]) {
          console.warn(`Missing required PayPal webhook header: ${header}`);
          return false;
        }
      }

      // TODO: Implement proper signature validation
      // This would involve calling PayPal's webhook verification API
      
      return true;
    } catch (error) {
      console.error('PayPal webhook validation error:', error);
      return false;
    }
  }

  static calculatePlatformFee(amount: number, feePercentage: number = 0.05): number {
    return Math.round(amount * feePercentage * 100) / 100;
  }

  static calculateSellerEarnings(amount: number, platformFee: number): number {
    return Math.round((amount - platformFee) * 100) / 100;
  }
}
