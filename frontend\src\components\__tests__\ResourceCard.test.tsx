import { describe, it, expect, vi } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import { ResourceCard } from '../ResourceCard'
import { render, mockResource } from '@/test/test-utils'

describe('ResourceCard', () => {
  const mockOnPurchase = vi.fn()

  beforeEach(() => {
    mockOnPurchase.mockClear()
  })

  it('renders resource information correctly', () => {
    render(
      <ResourceCard 
        resource={mockResource} 
        onPurchase={mockOnPurchase}
      />
    )

    expect(screen.getByText(mockResource.title)).toBeInTheDocument()
    expect(screen.getByText(mockResource.description)).toBeInTheDocument()
    expect(screen.getByText(mockResource.creator.username)).toBeInTheDocument()
    expect(screen.getByText('$29.99')).toBeInTheDocument()
  })

  it('displays resource type badge', () => {
    render(
      <ResourceCard 
        resource={mockResource} 
        onPurchase={mockOnPurchase}
      />
    )

    expect(screen.getByText('Fine-tuned Model')).toBeInTheDocument()
  })

  it('shows featured badge when resource is featured', () => {
    const featuredResource = { ...mockResource, isFeatured: true }
    
    render(
      <ResourceCard 
        resource={featuredResource} 
        onPurchase={mockOnPurchase}
      />
    )

    expect(screen.getByText('Featured')).toBeInTheDocument()
  })

  it('displays rating and download count', () => {
    render(
      <ResourceCard 
        resource={mockResource} 
        onPurchase={mockOnPurchase}
      />
    )

    expect(screen.getByText('4.5')).toBeInTheDocument()
    expect(screen.getByText('(10)')).toBeInTheDocument()
    expect(screen.getByText('100')).toBeInTheDocument()
  })

  it('shows tags when available', () => {
    render(
      <ResourceCard 
        resource={mockResource} 
        onPurchase={mockOnPurchase}
      />
    )

    expect(screen.getByText('AI')).toBeInTheDocument()
    expect(screen.getByText('Machine Learning')).toBeInTheDocument()
    expect(screen.getByText('Test')).toBeInTheDocument()
  })

  it('calls onPurchase when purchase button is clicked', () => {
    render(
      <ResourceCard 
        resource={mockResource} 
        onPurchase={mockOnPurchase}
      />
    )

    const purchaseButton = screen.getByText('$29.99')
    fireEvent.click(purchaseButton)

    expect(mockOnPurchase).toHaveBeenCalledWith(mockResource.id)
  })

  it('shows download button for free resources', () => {
    const freeResource = { ...mockResource, price: 0 }
    
    render(
      <ResourceCard 
        resource={freeResource} 
        onPurchase={mockOnPurchase}
      />
    )

    expect(screen.getByText('Download')).toBeInTheDocument()
  })

  it('hides purchase button when showPurchaseButton is false', () => {
    render(
      <ResourceCard 
        resource={mockResource} 
        onPurchase={mockOnPurchase}
        showPurchaseButton={false}
      />
    )

    expect(screen.queryByText('$29.99')).not.toBeInTheDocument()
  })

  it('shows verified badge for verified creators', () => {
    render(
      <ResourceCard 
        resource={mockResource} 
        onPurchase={mockOnPurchase}
      />
    )

    // Check for verified icon (using aria-label or data-testid would be better)
    const verifiedElements = screen.getAllByRole('img')
    expect(verifiedElements.length).toBeGreaterThan(0)
  })

  it('renders thumbnail image when available', () => {
    render(
      <ResourceCard 
        resource={mockResource} 
        onPurchase={mockOnPurchase}
      />
    )

    const thumbnail = screen.getByAltText(mockResource.title)
    expect(thumbnail).toBeInTheDocument()
    expect(thumbnail).toHaveAttribute('src', mockResource.thumbnailUrl)
  })

  it('shows fallback icon when no thumbnail is available', () => {
    const resourceWithoutThumbnail = { ...mockResource, thumbnailUrl: undefined }
    
    render(
      <ResourceCard 
        resource={resourceWithoutThumbnail} 
        onPurchase={mockOnPurchase}
      />
    )

    // Should show the robot emoji for fine_tuned_model type
    expect(screen.getByText('🤖')).toBeInTheDocument()
  })
})
