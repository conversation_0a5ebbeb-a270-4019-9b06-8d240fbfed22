#!/bin/bash

# AIGC Service Hub AWS Deployment Script
# This script automates the deployment process to AWS

set -e

# Configuration
PROJECT_NAME="aigc-service-hub"
AWS_REGION="us-west-2"
ECR_REPOSITORY_URI=""
ECS_CLUSTER_NAME="${PROJECT_NAME}-cluster"
ECS_SERVICE_NAME="${PROJECT_NAME}-service"
TASK_DEFINITION_NAME="${PROJECT_NAME}-task"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if AWS CLI is installed
    if ! command -v aws &> /dev/null; then
        error "AWS CLI is not installed. Please install it first."
    fi
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install it first."
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        error "AWS credentials not configured. Please run 'aws configure'."
    fi
    
    log "Prerequisites check passed!"
}

# Create ECR repository if it doesn't exist
create_ecr_repository() {
    log "Creating ECR repository..."
    
    # Check if repository exists
    if aws ecr describe-repositories --repository-names ${PROJECT_NAME} --region ${AWS_REGION} &> /dev/null; then
        log "ECR repository already exists"
    else
        aws ecr create-repository \
            --repository-name ${PROJECT_NAME} \
            --region ${AWS_REGION} \
            --image-scanning-configuration scanOnPush=true
        log "ECR repository created"
    fi
    
    # Get repository URI
    ECR_REPOSITORY_URI=$(aws ecr describe-repositories \
        --repository-names ${PROJECT_NAME} \
        --region ${AWS_REGION} \
        --query 'repositories[0].repositoryUri' \
        --output text)
    
    log "ECR Repository URI: ${ECR_REPOSITORY_URI}"
}

# Build and push Docker images
build_and_push_images() {
    log "Building and pushing Docker images..."
    
    # Login to ECR
    aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${ECR_REPOSITORY_URI}
    
    # Build frontend image
    log "Building frontend image..."
    docker build -t ${PROJECT_NAME}-frontend:latest ./frontend -f ./frontend/Dockerfile.prod
    docker tag ${PROJECT_NAME}-frontend:latest ${ECR_REPOSITORY_URI}:frontend-latest
    docker push ${ECR_REPOSITORY_URI}:frontend-latest
    
    # Build backend image
    log "Building backend image..."
    docker build -t ${PROJECT_NAME}-backend:latest ./backend -f ./backend/Dockerfile.prod
    docker tag ${PROJECT_NAME}-backend:latest ${ECR_REPOSITORY_URI}:backend-latest
    docker push ${ECR_REPOSITORY_URI}:backend-latest
    
    log "Images pushed successfully!"
}

# Create ECS cluster
create_ecs_cluster() {
    log "Creating ECS cluster..."
    
    if aws ecs describe-clusters --clusters ${ECS_CLUSTER_NAME} --region ${AWS_REGION} &> /dev/null; then
        log "ECS cluster already exists"
    else
        aws ecs create-cluster \
            --cluster-name ${ECS_CLUSTER_NAME} \
            --region ${AWS_REGION}
        log "ECS cluster created"
    fi
}

# Deploy to ECS
deploy_to_ecs() {
    log "Deploying to ECS..."
    
    # Register task definition
    aws ecs register-task-definition \
        --cli-input-json file://aws/task-definition.json \
        --region ${AWS_REGION}
    
    # Update service
    if aws ecs describe-services --cluster ${ECS_CLUSTER_NAME} --services ${ECS_SERVICE_NAME} --region ${AWS_REGION} &> /dev/null; then
        log "Updating existing ECS service..."
        aws ecs update-service \
            --cluster ${ECS_CLUSTER_NAME} \
            --service ${ECS_SERVICE_NAME} \
            --task-definition ${TASK_DEFINITION_NAME} \
            --region ${AWS_REGION}
    else
        log "Creating new ECS service..."
        aws ecs create-service \
            --cluster ${ECS_CLUSTER_NAME} \
            --service-name ${ECS_SERVICE_NAME} \
            --task-definition ${TASK_DEFINITION_NAME} \
            --desired-count 2 \
            --region ${AWS_REGION}
    fi
    
    log "Deployment completed!"
}

# Health check
health_check() {
    log "Performing health check..."
    
    # Wait for service to be stable
    aws ecs wait services-stable \
        --cluster ${ECS_CLUSTER_NAME} \
        --services ${ECS_SERVICE_NAME} \
        --region ${AWS_REGION}
    
    log "Service is stable and healthy!"
}

# Main deployment function
main() {
    log "Starting AWS deployment for ${PROJECT_NAME}..."
    
    check_prerequisites
    create_ecr_repository
    build_and_push_images
    create_ecs_cluster
    deploy_to_ecs
    health_check
    
    log "Deployment completed successfully!"
    log "Your application should be available at the configured load balancer URL."
}

# Run main function
main "$@"
