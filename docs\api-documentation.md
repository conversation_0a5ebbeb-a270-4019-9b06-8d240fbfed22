# AIGC Service Hub - API Documentation

## Base URL
- Development: `http://localhost:3001/api`
- Production: `https://api.aigc-service-hub.com/api`

## Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Response Format
All API responses follow this standard format:

### Success Response
```json
{
  "success": true,
  "data": {}, // Response data
  "message": "Success message",
  "timestamp": "2025-07-09T12:00:00.000Z"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": {} // Additional error details
  },
  "timestamp": "2025-07-09T12:00:00.000Z"
}
```

## Authentication Endpoints

### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "username": "username",
  "fullName": "<PERSON>",
  "userType": "individual" // or "enterprise"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "username": "username",
      "fullName": "John Doe",
      "userType": "individual"
    },
    "token": "jwt_token"
  }
}
```

### POST /auth/login
Authenticate user and get access token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

### POST /auth/logout
Logout user (invalidate token).

### GET /auth/profile
Get current user profile information.

### PUT /auth/profile
Update user profile information.

## User Management Endpoints

### GET /users/:id
Get user public profile.

### PUT /users/:id/profile
Update extended user profile.

**Request Body:**
```json
{
  "companyName": "Company Name",
  "websiteUrl": "https://example.com",
  "socialLinks": {
    "twitter": "https://twitter.com/username",
    "linkedin": "https://linkedin.com/in/username"
  },
  "skills": ["AI", "Machine Learning", "Python"],
  "experienceLevel": "advanced"
}
```

## Resource Management Endpoints

### GET /resources
Get paginated list of resources with filtering and sorting.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)
- `category`: Filter by category ID
- `type`: Filter by resource type
- `search`: Search in title and description
- `tags`: Filter by tags (comma-separated)
- `minPrice`: Minimum price filter
- `maxPrice`: Maximum price filter
- `sortBy`: Sort field (created_at, price, rating, downloads)
- `sortOrder`: Sort order (asc, desc)

**Response:**
```json
{
  "success": true,
  "data": {
    "resources": [
      {
        "id": "uuid",
        "title": "Resource Title",
        "description": "Resource description",
        "resourceType": "fine_tuned_model",
        "category": {
          "id": "uuid",
          "name": "Category Name",
          "slug": "category-slug"
        },
        "creator": {
          "id": "uuid",
          "username": "creator",
          "fullName": "Creator Name"
        },
        "price": 29.99,
        "currency": "USD",
        "thumbnailUrl": "https://...",
        "tags": ["ai", "nlp"],
        "downloadCount": 150,
        "ratingAverage": 4.5,
        "ratingCount": 23,
        "createdAt": "2025-07-09T12:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "pages": 8
    }
  }
}
```

### POST /resources
Create a new resource (requires authentication).

**Request Body:**
```json
{
  "title": "Resource Title",
  "description": "Detailed description",
  "resourceType": "fine_tuned_model",
  "categoryId": "uuid",
  "price": 29.99,
  "tags": ["ai", "nlp", "gpt"]
}
```

### GET /resources/:id
Get detailed resource information.

### PUT /resources/:id
Update resource information (creator only).

### DELETE /resources/:id
Delete resource (creator only).

### POST /resources/:id/upload
Upload resource files (creator only).

**Request:** Multipart form data with files.

## Category Endpoints

### GET /categories
Get all active categories.

**Response:**
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": "uuid",
        "name": "Fine-tuned Models",
        "slug": "fine-tuned-models",
        "description": "Customized AI models",
        "iconUrl": "https://...",
        "resourceCount": 45
      }
    ]
  }
}
```

## Transaction Endpoints

### POST /transactions
Create a new transaction (purchase).

**Request Body:**
```json
{
  "resourceId": "uuid",
  "paymentMethodId": "uuid"
}
```

### GET /transactions
Get user's transaction history.

### GET /transactions/:id
Get specific transaction details.

### POST /payments/paypal/create
Create PayPal payment.

### POST /payments/paypal/execute
Execute PayPal payment after approval.

### POST /payments/webhook
PayPal webhook endpoint for payment notifications.

## Review Endpoints

### GET /resources/:id/reviews
Get reviews for a resource.

### POST /resources/:id/reviews
Create a review (requires verified purchase).

**Request Body:**
```json
{
  "rating": 5,
  "comment": "Excellent resource!"
}
```

## Search Endpoints

### GET /search/resources
Advanced resource search.

**Query Parameters:**
- `q`: Search query
- `filters`: JSON object with filters
- `facets`: Requested facets for filtering UI

## User Collections

### GET /collections
Get user's collections.

### POST /collections
Create a new collection.

### PUT /collections/:id
Update collection.

### DELETE /collections/:id
Delete collection.

### POST /collections/:id/resources
Add resource to collection.

### DELETE /collections/:id/resources/:resourceId
Remove resource from collection.

## Favorites

### GET /favorites
Get user's favorite resources.

### POST /favorites
Add resource to favorites.

### DELETE /favorites/:resourceId
Remove resource from favorites.

## Earnings & Withdrawals

### GET /earnings
Get creator earnings summary.

### GET /earnings/transactions
Get detailed earnings transactions.

### POST /withdrawals
Request earnings withdrawal.

### GET /withdrawals
Get withdrawal history.

## File Management

### POST /upload/avatar
Upload user avatar.

### POST /upload/resource-thumbnail
Upload resource thumbnail.

### POST /upload/resource-files
Upload resource files.

## Notifications

### GET /notifications
Get user notifications.

### PUT /notifications/:id/read
Mark notification as read.

### PUT /notifications/read-all
Mark all notifications as read.

## Admin Endpoints (Admin only)

### GET /admin/resources/pending
Get resources pending approval.

### PUT /admin/resources/:id/approve
Approve resource.

### PUT /admin/resources/:id/reject
Reject resource.

### GET /admin/users
Get users list with admin controls.

### PUT /admin/users/:id/verify
Verify user account.

## Error Codes

- `VALIDATION_ERROR`: Request validation failed
- `UNAUTHORIZED`: Authentication required
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `CONFLICT`: Resource already exists
- `PAYMENT_FAILED`: Payment processing failed
- `UPLOAD_FAILED`: File upload failed
- `RATE_LIMIT_EXCEEDED`: Too many requests

## Rate Limits

- General API: 100 requests per 15 minutes per IP
- Authentication: 5 requests per minute per IP
- File uploads: 10 requests per hour per user
- Search: 60 requests per minute per user
