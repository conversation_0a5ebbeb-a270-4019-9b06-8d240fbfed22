import React from 'react'
import { AppLayout } from '@/components/Layout/AppLayout'
import { HeroBanner } from '@/components/HeroBanner'
import { FeaturedSection } from '@/components/FeaturedSection'
import { WaterfallGrid } from '@/components/WaterfallGrid'

export const NewHome: React.FC = () => {
  return (
    <AppLayout>
      {/* H2 - 轮播图和英雄榜 | 高300 */}
      <HeroBanner />

      {/* H3 - 精选区三幅作品 | 高180 */}
      <FeaturedSection />

      {/* H4 - 资源展示区（瀑布流） | 高度自动延伸 */}
      <WaterfallGrid />
    </AppLayout>
  )
}
