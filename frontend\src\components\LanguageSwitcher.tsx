import React from 'react'
import { Dropdown, Button, Space } from 'antd'
import { GlobalOutlined, DownOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'

const LanguageSwitcher: React.FC = () => {
  const { i18n } = useTranslation()

  const languages = [
    {
      key: 'zh-CN',
      label: '中文（简体）',
      shortLabel: 'CN',
      flag: '🇨🇳'
    },
    {
      key: 'en-US',
      label: 'English',
      shortLabel: 'US',
      flag: '🇺🇸'
    }
  ]

  const currentLanguage = languages.find(lang => lang.key === i18n.language) || languages[0]

  const handleLanguageChange = (language: string) => {
    i18n.changeLanguage(language)
    localStorage.setItem('language', language)
  }

  const menuItems = languages.map(lang => ({
    key: lang.key,
    label: (
      <Space>
        <span style={{ fontSize: '16px' }}>{lang.flag}</span>
        <span style={{
          color: lang.key === i18n.language ? '#8B5CF6' : '#fff',
          fontWeight: 'normal',
          textTransform: 'uppercase',
          fontSize: '14px'
        }}>
          {lang.shortLabel}
        </span>
        {lang.key === i18n.language && (
          <span style={{ color: '#8B5CF6', fontSize: '12px' }}>✓</span>
        )}
      </Space>
    ),
    onClick: () => handleLanguageChange(lang.key)
  }))

  return (
    <Dropdown
      menu={{ items: menuItems }}
      placement="bottomRight"
      trigger={['hover']}
    >
      <Button
        type="default"
        size="large"
        style={{
          backgroundColor: '#2a2a2a',
          borderColor: '#3a3a3a',
          color: '#fff',
          borderRadius: '8px',
          display: 'flex',
          alignItems: 'center',
          fontSize: '14px',
          fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
          fontWeight: 'normal'
        }}
      >
        <Space>
          <GlobalOutlined style={{ fontSize: '14px' }} />
          <span className="hidden sm:inline" style={{ fontSize: '14px', textTransform: 'uppercase' }}>
            {currentLanguage.shortLabel}
          </span>
          <DownOutlined style={{ fontSize: '14px' }} />
        </Space>
      </Button>
    </Dropdown>
  )
}

export default LanguageSwitcher
