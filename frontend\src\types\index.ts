// User types
export interface User {
  id: string
  email: string
  username: string
  fullName: string
  userType: 'individual' | 'enterprise'
  avatarUrl?: string
  bio?: string
  isVerified: boolean
  createdAt: string
}

export interface UserProfile {
  id: string
  userId: string
  companyName?: string
  websiteUrl?: string
  socialLinks?: Record<string, string>
  skills?: string[]
  experienceLevel?: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  preferredCategories?: string[]
  notificationSettings?: Record<string, boolean>
}

// Resource types
export interface Resource {
  id: string
  title: string
  description: string
  resourceType: 'fine_tuned_model' | 'lora' | 'workflow' | 'prompt' | 'tool'
  category: Category
  creator: User
  price: number
  currency: string
  thumbnailUrl?: string
  tags: string[]
  downloadCount: number
  ratingAverage: number
  ratingCount: number
  isFeatured: boolean
  isActive: boolean
  status: 'pending' | 'approved' | 'rejected'
  createdAt: string
  updatedAt: string
}

export interface Category {
  id: string
  name: string
  slug: string
  description?: string
  iconUrl?: string
  isActive: boolean
  resourceCount?: number
}

// Transaction types
export interface Transaction {
  id: string
  buyerId: string
  sellerId: string
  resourceId: string
  amount: number
  currency: string
  platformFee: number
  sellerEarnings: number
  paymentMethod: string
  paymentId?: string
  status: 'pending' | 'completed' | 'failed' | 'refunded'
  createdAt: string
  completedAt?: string
}

// Review types
export interface Review {
  id: string
  resourceId: string
  reviewerId: string
  transactionId?: string
  rating: number
  comment?: string
  isVerifiedPurchase: boolean
  createdAt: string
  reviewer: User
}

// Collection types
export interface Collection {
  id: string
  userId: string
  name: string
  description?: string
  isPublic: boolean
  createdAt: string
  updatedAt: string
  resourceCount?: number
}

// Notification types
export interface Notification {
  id: string
  userId: string
  type: string
  title: string
  message: string
  data?: Record<string, any>
  isRead: boolean
  createdAt: string
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  message?: string
  timestamp: string
}

export interface PaginatedResponse<T> {
  items: T[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

// Form types
export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  email: string
  password: string
  confirmPassword: string
  username: string
  fullName: string
  userType: 'individual' | 'enterprise'
}

export interface ResourceForm {
  title: string
  description: string
  resourceType: string
  categoryId: string
  price: number
  tags: string[]
  files?: FileList
}

// Search types
export interface SearchFilters {
  category?: string
  type?: string
  minPrice?: number
  maxPrice?: number
  tags?: string[]
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface SearchParams extends SearchFilters {
  q?: string
  page?: number
  limit?: number
}
