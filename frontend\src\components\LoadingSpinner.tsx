import React from 'react'
import { Spin, Space, Typography } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'

const { Text } = Typography

interface LoadingSpinnerProps {
  size?: 'small' | 'default' | 'large'
  message?: string
  fullScreen?: boolean
  spinning?: boolean
  children?: React.ReactNode
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'default',
  message = 'Loading...',
  fullScreen = false,
  spinning = true,
  children
}) => {
  const antIcon = <LoadingOutlined style={{ fontSize: size === 'large' ? 48 : size === 'small' ? 16 : 24 }} spin />

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-80 flex items-center justify-center z-50">
        <Space direction="vertical" align="center" size="large">
          <Spin indicator={antIcon} size={size} />
          {message && <Text type="secondary">{message}</Text>}
        </Space>
      </div>
    )
  }

  if (children) {
    return (
      <Spin spinning={spinning} indicator={antIcon} tip={message}>
        {children}
      </Spin>
    )
  }

  return (
    <div className="flex items-center justify-center py-12">
      <Space direction="vertical" align="center">
        <Spin indicator={antIcon} size={size} />
        {message && <Text type="secondary">{message}</Text>}
      </Space>
    </div>
  )
}
