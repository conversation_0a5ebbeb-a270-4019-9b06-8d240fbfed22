import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Result, Button, Typography, Card } from 'antd'
import { BugOutlined, ReloadOutlined, HomeOutlined } from '@ant-design/icons'

const { Paragraph, Text } = Typography

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo)
    this.setState({ error, errorInfo })
  }

  handleReload = () => {
    window.location.reload()
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
          <Card className="max-w-2xl w-full">
            <Result
              status="error"
              icon={<BugOutlined />}
              title="Oops! Something went wrong"
              subTitle="We're sorry for the inconvenience. Please try refreshing the page or contact support if the problem persists."
              extra={[
                <Button 
                  type="primary" 
                  icon={<ReloadOutlined />}
                  onClick={this.handleReload}
                  key="reload"
                >
                  Reload Page
                </Button>,
                <Button 
                  icon={<HomeOutlined />}
                  onClick={this.handleGoHome}
                  key="home"
                >
                  Go Home
                </Button>
              ]}
            >
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div className="mt-6 text-left">
                  <Paragraph>
                    <Text strong>Error Details (Development Mode):</Text>
                  </Paragraph>
                  <div className="bg-gray-100 p-4 rounded-md overflow-auto">
                    <Text code className="text-red-600">
                      {this.state.error.toString()}
                    </Text>
                    {this.state.errorInfo && (
                      <pre className="mt-2 text-xs text-gray-600">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    )}
                  </div>
                </div>
              )}
            </Result>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}
