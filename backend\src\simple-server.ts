import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 3001;

// Basic middleware
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));

app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'AIGC Service Hub Backend is running!'
  });
});

// Test API endpoint
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'AIGC Service Hub API is working!',
    timestamp: new Date().toISOString()
  });
});

// Mock endpoints for frontend testing
app.get('/api/resources', (req, res) => {
  res.json({
    success: true,
    data: {
      resources: [
        {
          id: '1',
          title: 'AI Content Generator Pro',
          description: 'Advanced AI-powered content generation tool',
          resourceType: 'fine_tuned_model',
          price: 29.99,
          currency: 'USD',
          thumbnailUrl: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=240&fit=crop',
          tags: ['AI', 'Machine Learning', 'NLP'],
          creator: {
            id: 'user1',
            username: 'testuser',
            fullName: 'Test User',
            email: '<EMAIL>',
            isVerified: true
          },
          status: 'approved',
          isFeatured: true,
          ratingAverage: 4.8,
          ratingCount: 10,
          downloadCount: 100,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ],
      pagination: {
        page: 1,
        limit: 20,
        total: 1,
        pages: 1
      }
    },
    timestamp: new Date().toISOString()
  });
});

app.get('/api/categories', (req, res) => {
  res.json({
    success: true,
    data: {
      categories: [
        {
          id: 'cat1',
          name: 'AI Models',
          description: 'Fine-tuned AI models and adapters',
          slug: 'ai-models',
          isActive: true
        }
      ]
    },
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 AIGC Service Hub Backend running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log(`🧪 Test API: http://localhost:${PORT}/api/test`);
  console.log(`📦 Resources API: http://localhost:${PORT}/api/resources`);
});

export default app;
