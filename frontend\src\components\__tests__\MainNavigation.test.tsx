import { describe, it, expect, vi } from 'vitest'
import { screen } from '@testing-library/react'
import { MainNavigation } from '../Layout/MainNavigation'
import { render } from '@/test/test-utils'

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useLocation: () => ({ pathname: '/' }),
    Link: ({ children, to }: { children: React.ReactNode; to: string }) => (
      <a href={to}>{children}</a>
    )
  }
})

describe('MainNavigation', () => {
  it('renders all navigation items', () => {
    render(<MainNavigation />)
    
    // Check for main navigation items
    expect(screen.getByText('首页')).toBeInTheDocument()
    expect(screen.getByText('微调模型')).toBeInTheDocument()
    expect(screen.getByText('LoRA')).toBeInTheDocument()
    expect(screen.getByText('工作流')).toBeInTheDocument()
    expect(screen.getByText('提示词')).toBeInTheDocument()
    expect(screen.getByText('工具')).toBeInTheDocument()
    expect(screen.getByText('排行榜')).toBeInTheDocument()
    expect(screen.getByText('活动')).toBeInTheDocument()
  })

  it('has correct navigation links', () => {
    render(<MainNavigation />)
    
    // Check for correct href attributes
    const homeLink = screen.getByText('首页').closest('a')
    expect(homeLink).toHaveAttribute('href', '/')
    
    const modelsLink = screen.getByText('微调模型').closest('a')
    expect(modelsLink).toHaveAttribute('href', '/resources?type=fine_tuned_model')
    
    const loraLink = screen.getByText('LoRA').closest('a')
    expect(loraLink).toHaveAttribute('href', '/resources?type=lora')
    
    const workflowsLink = screen.getByText('工作流').closest('a')
    expect(workflowsLink).toHaveAttribute('href', '/resources?type=workflow')
    
    const promptsLink = screen.getByText('提示词').closest('a')
    expect(promptsLink).toHaveAttribute('href', '/resources?type=prompt')
    
    const toolsLink = screen.getByText('工具').closest('a')
    expect(toolsLink).toHaveAttribute('href', '/resources?type=tool')
  })

  it('has correct styling', () => {
    render(<MainNavigation />)
    
    const navigationContainer = screen.getByText('首页').closest('div')
    expect(navigationContainer).toHaveStyle('height: 50px')
    expect(navigationContainer).toHaveStyle('background-color: #1f1f1f')
  })

  it('renders with dark theme', () => {
    render(<MainNavigation />)
    
    // The menu should have dark theme styling
    const menu = screen.getByRole('menu')
    expect(menu).toHaveClass('ant-menu-dark')
  })

  it('has proper accessibility attributes', () => {
    render(<MainNavigation />)
    
    const menu = screen.getByRole('menu')
    expect(menu).toBeInTheDocument()
    
    // Check for menu items
    const menuItems = screen.getAllByRole('menuitem')
    expect(menuItems).toHaveLength(8) // 8 navigation items
  })

  it('displays icons for each menu item', () => {
    render(<MainNavigation />)
    
    // Check that icons are rendered (they should be present as span elements with anticon class)
    const icons = document.querySelectorAll('.anticon')
    expect(icons.length).toBeGreaterThan(0)
  })

  it('has horizontal menu mode', () => {
    render(<MainNavigation />)
    
    const menu = screen.getByRole('menu')
    expect(menu).toHaveClass('ant-menu-horizontal')
  })

  it('applies custom font styling', () => {
    render(<MainNavigation />)
    
    const menu = screen.getByRole('menu')
    expect(menu).toHaveStyle('font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif')
    expect(menu).toHaveStyle('font-size: 14px')
  })
})
