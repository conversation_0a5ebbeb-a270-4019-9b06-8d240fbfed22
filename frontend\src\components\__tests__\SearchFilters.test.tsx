import { describe, it, expect, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { SearchFilters } from '../SearchFilters'
import { render, mockCategory } from '@/test/test-utils'

describe('SearchFilters', () => {
  const mockOnFiltersChange = vi.fn()
  const mockOnSearch = vi.fn()
  
  const defaultProps = {
    filters: {
      page: 1,
      limit: 20,
      sortBy: 'created_at',
      sortOrder: 'DESC' as const
    },
    categories: [mockCategory],
    onFiltersChange: mockOnFiltersChange,
    onSearch: mockOnSearch,
    loading: false
  }

  beforeEach(() => {
    mockOnFiltersChange.mockClear()
    mockOnSearch.mockClear()
  })

  it('renders search input', () => {
    render(<SearchFilters {...defaultProps} />)
    
    const searchInput = screen.getByPlaceholderText(/search ai resources/i)
    expect(searchInput).toBeInTheDocument()
  })

  it('renders category filter', () => {
    render(<SearchFilters {...defaultProps} />)
    
    expect(screen.getByText('Category')).toBeInTheDocument()
    // Check for the select component
    const categorySelect = screen.getByText('All Categories')
    expect(categorySelect).toBeInTheDocument()
  })

  it('renders resource type filter', () => {
    render(<SearchFilters {...defaultProps} />)
    
    expect(screen.getByText('Resource Type')).toBeInTheDocument()
    expect(screen.getByText('All Types')).toBeInTheDocument()
  })

  it('renders sort options', () => {
    render(<SearchFilters {...defaultProps} />)
    
    expect(screen.getByText('Sort By')).toBeInTheDocument()
    expect(screen.getByText('Order')).toBeInTheDocument()
  })

  it('calls onSearch when search button is clicked', async () => {
    render(<SearchFilters {...defaultProps} />)
    
    const searchInput = screen.getByPlaceholderText(/search ai resources/i)
    const searchButton = screen.getByRole('button', { name: /search/i })
    
    fireEvent.change(searchInput, { target: { value: 'test query' } })
    fireEvent.click(searchButton)
    
    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalled()
    })
  })

  it('calls onFiltersChange when search input changes', async () => {
    render(<SearchFilters {...defaultProps} />)
    
    const searchInput = screen.getByPlaceholderText(/search ai resources/i)
    
    fireEvent.change(searchInput, { target: { value: 'test query' } })
    
    await waitFor(() => {
      expect(mockOnFiltersChange).toHaveBeenCalledWith({
        ...defaultProps.filters,
        q: 'test query'
      })
    })
  })

  it('shows clear button and clears filters when clicked', async () => {
    const filtersWithData = {
      ...defaultProps.filters,
      q: 'test query',
      category: 'cat1'
    }
    
    render(<SearchFilters {...defaultProps} filters={filtersWithData} />)
    
    const clearButton = screen.getByText('Clear')
    fireEvent.click(clearButton)
    
    await waitFor(() => {
      expect(mockOnFiltersChange).toHaveBeenCalledWith({
        q: '',
        page: 1,
        limit: 20
      })
    })
  })

  it('shows advanced filters when expanded', async () => {
    render(<SearchFilters {...defaultProps} />)
    
    const advancedFiltersButton = screen.getByText('Advanced Filters')
    fireEvent.click(advancedFiltersButton)
    
    await waitFor(() => {
      expect(screen.getByText(/price range/i)).toBeInTheDocument()
      expect(screen.getByText('Popular Tags')).toBeInTheDocument()
    })
  })

  it('displays active filters when present', () => {
    const filtersWithActive = {
      ...defaultProps.filters,
      category: 'cat1',
      type: 'fine_tuned_model',
      tags: ['AI', 'ML']
    }
    
    render(<SearchFilters {...defaultProps} filters={filtersWithActive} />)
    
    expect(screen.getByText('Active Filters:')).toBeInTheDocument()
    expect(screen.getByText('Category: AI Models')).toBeInTheDocument()
    expect(screen.getByText('Type: Fine-tuned Models')).toBeInTheDocument()
    expect(screen.getByText('AI')).toBeInTheDocument()
    expect(screen.getByText('ML')).toBeInTheDocument()
  })

  it('shows loading state on search input', () => {
    render(<SearchFilters {...defaultProps} loading={true} />)
    
    const searchInput = screen.getByPlaceholderText(/search ai resources/i)
    expect(searchInput).toBeInTheDocument()
    // The loading state would be on the search button or input
  })

  it('handles price range changes', async () => {
    render(<SearchFilters {...defaultProps} />)
    
    // Expand advanced filters first
    const advancedFiltersButton = screen.getByText('Advanced Filters')
    fireEvent.click(advancedFiltersButton)
    
    await waitFor(() => {
      expect(screen.getByText(/price range/i)).toBeInTheDocument()
    })
    
    // Price range slider interaction would need more specific testing
    // This is a basic check that the component renders
  })

  it('handles tag selection', async () => {
    render(<SearchFilters {...defaultProps} />)
    
    // Expand advanced filters
    const advancedFiltersButton = screen.getByText('Advanced Filters')
    fireEvent.click(advancedFiltersButton)
    
    await waitFor(() => {
      const aiTag = screen.getByText('AI')
      expect(aiTag).toBeInTheDocument()
    })
  })
})
