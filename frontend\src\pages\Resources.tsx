import React, { useState, useEffect } from 'react'
import { Pagination, message, Spin } from 'antd'
import { useTranslation } from 'react-i18next'
import { AppLayout } from '@/components/Layout/AppLayout'
import { SearchFilters } from '@/components/SearchFilters'
import { ResourceGrid } from '@/components/ResourceGrid'
import { useAuth } from '@/hooks/useAuth'
import { resourcesApi, categoriesApi } from '@/services/api'
import { Resource, Category, SearchParams } from '@/types'

export const Resources: React.FC = () => {
  const { isAuthenticated } = useAuth()
  const { t } = useTranslation()
  const [resources, setResources] = useState<Resource[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [filters, setFilters] = useState<SearchParams>({
    page: 1,
    limit: 20,
    sortBy: 'created_at',
    sortOrder: 'DESC'
  })

  // Load categories on mount
  useEffect(() => {
    loadCategories()
  }, [])

  // Load resources when filters change
  useEffect(() => {
    loadResources()
  }, [filters])

  const loadCategories = async () => {
    try {
      const response = await categoriesApi.getCategories()
      if (response.data.success) {
        setCategories(response.data.data.categories)
      }
    } catch (error) {
      console.error('Failed to load categories:', error)
    }
  }

  const loadResources = async () => {
    setLoading(true)
    try {
      const response = await resourcesApi.getResources(filters)
      if (response.data.success) {
        setResources(response.data.data.resources)
        setTotal(response.data.data.pagination.total)
      }
    } catch (error) {
      console.error('Failed to load resources:', error)
      message.error('加载资源失败')
    } finally {
      setLoading(false)
    }
  }

  const handleFiltersChange = (newFilters: SearchParams) => {
    setFilters({
      ...newFilters,
      page: 1 // Reset to first page when filters change
    })
  }

  const handleSearch = () => {
    loadResources()
  }

  const handlePageChange = (page: number, pageSize?: number) => {
    setFilters({
      ...filters,
      page,
      limit: pageSize || filters.limit
    })
  }

  const handlePurchase = async (resourceId: string) => {
    if (!isAuthenticated) {
      message.warning('请先登录以购买资源')
      return
    }

    try {
      // TODO: Implement purchase flow
      message.info('购买功能即将推出')
    } catch (error) {
      console.error('Purchase failed:', error)
      message.error('购买失败')
    }
  }

  return (
    <AppLayout>
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '24px' }}>
        <div style={{ marginBottom: '32px' }}>
          <h1 style={{ fontSize: '32px', fontWeight: 'bold', color: '#fff', marginBottom: '8px' }}>
            {t('resources.title')}
          </h1>
          <p style={{ color: '#888' }}>
            {t('resources.subtitle')}
          </p>
        </div>

        <SearchFilters
          filters={filters}
          categories={categories}
          onFiltersChange={handleFiltersChange}
          onSearch={handleSearch}
          loading={loading}
        />

        <div style={{ marginBottom: '24px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <p style={{ color: '#888' }}>
              {loading ? '加载中...' : `找到 ${total} 个资源`}
            </p>
          </div>
        </div>

        <ResourceGrid
          resources={resources}
          loading={loading}
          onPurchase={handlePurchase}
          emptyMessage="未找到符合条件的资源"
        />

        {total > 0 && (
          <div style={{ marginTop: '32px', display: 'flex', justifyContent: 'center' }}>
            <Pagination
              current={filters.page}
              pageSize={filters.limit}
              total={total}
              onChange={handlePageChange}
              showSizeChanger
              showQuickJumper
              showTotal={(total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 个资源`
              }
              pageSizeOptions={['12', '20', '40', '60']}
            />
          </div>
        )}
      </div>
    </AppLayout>
  )
}
