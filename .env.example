# AIGC Service Hub Environment Variables Template
# Copy this file to .env and fill in your actual values

# Application Environment
NODE_ENV=development
PORT=3001

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=aigc_service_hub
DB_USER=aigc_user
DB_PASSWORD=aigc_password

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# PayPal Configuration
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_MODE=sandbox  # sandbox or live

# AWS Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-west-2
AWS_S3_BUCKET=aigc-service-hub-files

# Frontend Configuration
VITE_API_BASE_URL=http://localhost:3001/api
VITE_PAYPAL_CLIENT_ID=your-paypal-client-id
VITE_APP_ENV=development

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# Logging
LOG_LEVEL=debug  # debug, info, warn, error

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=.zip,.tar.gz,.json,.txt,.md,.py,.js,.ts

# Production specific (for docker-compose.prod.yml)
# DB_USER=production_user
# DB_PASSWORD=secure_production_password
# JWT_SECRET=super-secure-production-jwt-secret
# API_BASE_URL=https://api.aigc-service-hub.com
