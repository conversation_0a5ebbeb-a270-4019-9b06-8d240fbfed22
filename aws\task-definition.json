{"family": "aigc-service-hub-task", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "executionRoleArn": "arn:aws:iam::ACCOUNT_ID:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::ACCOUNT_ID:role/ecsTaskRole", "containerDefinitions": [{"name": "frontend", "image": "ACCOUNT_ID.dkr.ecr.us-west-2.amazonaws.com/aigc-service-hub:frontend-latest", "portMappings": [{"containerPort": 80, "protocol": "tcp"}], "essential": true, "environment": [{"name": "NODE_ENV", "value": "production"}, {"name": "REACT_APP_API_URL", "value": "https://api.aigc-service-hub.com"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/aigc-service-hub", "awslogs-region": "us-west-2", "awslogs-stream-prefix": "frontend"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}}, {"name": "backend", "image": "ACCOUNT_ID.dkr.ecr.us-west-2.amazonaws.com/aigc-service-hub:backend-latest", "portMappings": [{"containerPort": 3001, "protocol": "tcp"}], "essential": true, "environment": [{"name": "NODE_ENV", "value": "production"}, {"name": "PORT", "value": "3001"}], "secrets": [{"name": "DATABASE_URL", "valueFrom": "arn:aws:secretsmanager:us-west-2:ACCOUNT_ID:secret:aigc-service-hub/database-url"}, {"name": "JWT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-west-2:ACCOUNT_ID:secret:aigc-service-hub/jwt-secret"}, {"name": "AWS_ACCESS_KEY_ID", "valueFrom": "arn:aws:secretsmanager:us-west-2:ACCOUNT_ID:secret:aigc-service-hub/aws-access-key"}, {"name": "AWS_SECRET_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:us-west-2:ACCOUNT_ID:secret:aigc-service-hub/aws-secret-key"}, {"name": "S3_BUCKET_NAME", "valueFrom": "arn:aws:secretsmanager:us-west-2:ACCOUNT_ID:secret:aigc-service-hub/s3-bucket"}, {"name": "PAYPAL_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-west-2:ACCOUNT_ID:secret:aigc-service-hub/paypal-client-id"}, {"name": "PAYPAL_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-west-2:ACCOUNT_ID:secret:aigc-service-hub/paypal-client-secret"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/aigc-service-hub", "awslogs-region": "us-west-2", "awslogs-stream-prefix": "backend"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:3001/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}}]}