import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { ConfigProvider } from 'antd'
import { ErrorBoundary } from '@/components/ErrorBoundary'
import { NotificationProvider } from '@/components/NotificationProvider'

// Mock auth hook for testing
const mockAuthContext = {
  isAuthenticated: false,
  user: null,
  login: vi.fn(),
  logout: vi.fn(),
  register: vi.fn(),
  loading: false,
  error: null
}

vi.mock('@/hooks/useAuth', () => ({
  useAuth: () => mockAuthContext
}))

// Custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <ErrorBoundary>
      <ConfigProvider>
        <NotificationProvider>
          <BrowserRouter>
            {children}
          </BrowserRouter>
        </NotificationProvider>
      </ConfigProvider>
    </ErrorBoundary>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

// Re-export everything
export * from '@testing-library/react'
export { customRender as render }

// Mock API responses
export const mockApiResponse = {
  success: true,
  data: {},
  timestamp: new Date().toISOString()
}

export const mockResource = {
  id: '1',
  title: 'Test AI Model',
  description: 'A test AI model for testing purposes',
  resourceType: 'fine_tuned_model' as const,
  price: 29.99,
  currency: 'USD',
  thumbnailUrl: 'https://example.com/thumbnail.jpg',
  downloadUrl: 'https://example.com/download.zip',
  tags: ['AI', 'Machine Learning', 'Test'],
  categoryId: 'cat1',
  creatorId: 'user1',
  creator: {
    id: 'user1',
    username: 'testuser',
    fullName: 'Test User',
    email: '<EMAIL>',
    avatarUrl: 'https://example.com/avatar.jpg',
    isVerified: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  status: 'approved' as const,
  isFeatured: false,
  ratingAverage: 4.5,
  ratingCount: 10,
  downloadCount: 100,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}

export const mockCategory = {
  id: 'cat1',
  name: 'AI Models',
  description: 'Fine-tuned AI models and adapters',
  slug: 'ai-models',
  parentId: null,
  isActive: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}

export const mockUser = {
  id: 'user1',
  username: 'testuser',
  fullName: 'Test User',
  email: '<EMAIL>',
  avatarUrl: 'https://example.com/avatar.jpg',
  isVerified: true,
  role: 'user' as const,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}

// Helper functions for testing
export const mockAuthenticatedUser = () => {
  mockAuthContext.isAuthenticated = true
  mockAuthContext.user = mockUser
}

export const mockUnauthenticatedUser = () => {
  mockAuthContext.isAuthenticated = false
  mockAuthContext.user = null
}

// Mock fetch responses
export const mockFetch = (response: any, ok = true) => {
  global.fetch = vi.fn(() =>
    Promise.resolve({
      ok,
      json: () => Promise.resolve(response),
      status: ok ? 200 : 400,
      statusText: ok ? 'OK' : 'Bad Request'
    })
  ) as any
}
