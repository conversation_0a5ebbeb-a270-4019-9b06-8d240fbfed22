import { describe, it, expect, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { WaterfallGrid } from '../WaterfallGrid'
import { render } from '@/test/test-utils'

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => vi.fn()
  }
})

// Mock InfiniteScroll
vi.mock('react-infinite-scroll-component', () => ({
  default: ({ children, loader, endMessage }: any) => (
    <div data-testid="infinite-scroll">
      {children}
      {loader}
      {endMessage}
    </div>
  )
}))

describe('WaterfallGrid', () => {
  it('renders section title', () => {
    render(<WaterfallGrid />)
    
    expect(screen.getByText('所有资源')).toBeInTheDocument()
    expect(screen.getByText('随时由创作者上传更新')).toBeInTheDocument()
  })

  it('displays loading spinner initially', () => {
    render(<WaterfallGrid />)
    
    const spinner = document.querySelector('.ant-spin')
    expect(spinner).toBeInTheDocument()
  })

  it('shows resource cards after loading', async () => {
    render(<WaterfallGrid />)
    
    // Wait for initial data to load
    await waitFor(() => {
      const cards = document.querySelectorAll('.waterfall-resource-card')
      expect(cards.length).toBeGreaterThan(0)
    }, { timeout: 2000 })
  })

  it('displays resource information', async () => {
    render(<WaterfallGrid />)
    
    await waitFor(() => {
      // Check for resource titles (mock data pattern)
      const titleElements = screen.getAllByText(/资源 \d+/)
      expect(titleElements.length).toBeGreaterThan(0)
    }, { timeout: 2000 })
  })

  it('shows creator information', async () => {
    render(<WaterfallGrid />)
    
    await waitFor(() => {
      // Check for creator names (mock data pattern)
      const creatorElements = screen.getAllByText(/创作者\d+/)
      expect(creatorElements.length).toBeGreaterThan(0)
    }, { timeout: 2000 })
  })

  it('displays resource type tags', async () => {
    render(<WaterfallGrid />)
    
    await waitFor(() => {
      // Check for type tags
      const typeTags = document.querySelectorAll('.ant-tag')
      expect(typeTags.length).toBeGreaterThan(0)
    }, { timeout: 2000 })
  })

  it('shows statistics for resources', async () => {
    render(<WaterfallGrid />)
    
    await waitFor(() => {
      // Check for download icons
      const downloadIcons = document.querySelectorAll('.anticon-download')
      expect(downloadIcons.length).toBeGreaterThan(0)
      
      // Check for eye icons (views)
      const eyeIcons = document.querySelectorAll('.anticon-eye')
      expect(eyeIcons.length).toBeGreaterThan(0)
      
      // Check for heart icons (likes)
      const heartIcons = document.querySelectorAll('.anticon-heart')
      expect(heartIcons.length).toBeGreaterThan(0)
    }, { timeout: 2000 })
  })

  it('handles resource card clicks', async () => {
    const mockNavigate = vi.fn()
    vi.mocked(require('react-router-dom').useNavigate).mockReturnValue(mockNavigate)
    
    render(<WaterfallGrid />)
    
    await waitFor(() => {
      const firstCard = document.querySelector('.waterfall-resource-card')
      if (firstCard) {
        fireEvent.click(firstCard)
        expect(mockNavigate).toHaveBeenCalled()
      }
    }, { timeout: 2000 })
  })

  it('handles creator clicks', async () => {
    const mockNavigate = vi.fn()
    vi.mocked(require('react-router-dom').useNavigate).mockReturnValue(mockNavigate)
    
    render(<WaterfallGrid />)
    
    await waitFor(() => {
      const creatorElement = screen.getAllByText(/创作者\d+/)[0]
      if (creatorElement) {
        fireEvent.click(creatorElement)
        expect(mockNavigate).toHaveBeenCalled()
      }
    }, { timeout: 2000 })
  })

  it('applies hover effects to cards', async () => {
    render(<WaterfallGrid />)
    
    await waitFor(() => {
      const firstCard = document.querySelector('.waterfall-resource-card')
      if (firstCard) {
        fireEvent.mouseEnter(firstCard)
        expect(firstCard).toHaveStyle('transform: translateY(-4px)')
        
        fireEvent.mouseLeave(firstCard)
        expect(firstCard).toHaveStyle('transform: translateY(0)')
      }
    }, { timeout: 2000 })
  })

  it('has correct grid layout', () => {
    render(<WaterfallGrid />)
    
    const gridContainer = document.querySelector('[style*="grid-template-columns"]')
    expect(gridContainer).toBeInTheDocument()
    expect(gridContainer).toHaveStyle('grid-template-columns: repeat(auto-fill, minmax(280px, 1fr))')
  })

  it('displays featured labels for featured resources', async () => {
    render(<WaterfallGrid />)
    
    await waitFor(() => {
      // Some resources should have featured labels
      const featuredLabels = screen.getAllByText('精选')
      expect(featuredLabels.length).toBeGreaterThan(0)
    }, { timeout: 2000 })
  })

  it('shows infinite scroll component', () => {
    render(<WaterfallGrid />)
    
    const infiniteScroll = screen.getByTestId('infinite-scroll')
    expect(infiniteScroll).toBeInTheDocument()
  })

  it('has proper section styling', () => {
    render(<WaterfallGrid />)
    
    const section = screen.getByText('所有资源').closest('div')?.parentElement
    expect(section).toHaveStyle('background-color: #1a1a1a')
    expect(section).toHaveStyle('padding: 24px')
  })

  it('displays database icon in title', () => {
    render(<WaterfallGrid />)
    
    const databaseIcon = document.querySelector('.anticon-database')
    expect(databaseIcon).toBeInTheDocument()
  })

  it('maintains 3:1 image to text ratio', async () => {
    render(<WaterfallGrid />)
    
    await waitFor(() => {
      const imageAreas = document.querySelectorAll('[style*="flex: 3"]')
      const textAreas = document.querySelectorAll('[style*="flex: 1"]')
      
      expect(imageAreas.length).toBeGreaterThan(0)
      expect(textAreas.length).toBeGreaterThan(0)
    }, { timeout: 2000 })
  })
})
