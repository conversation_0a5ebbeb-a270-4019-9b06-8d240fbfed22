# Development Dockerfile for Backend
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies and development tools
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    curl \
    git \
    && rm -rf /var/cache/apk/*

# Install nodemon globally for development
RUN npm install -g nodemon

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm install

# Copy source code
COPY . .

# Create logs directory
RUN mkdir -p logs

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Change ownership of the app directory
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Start the application with nodemon for hot reload
CMD ["npm", "run", "dev"]
