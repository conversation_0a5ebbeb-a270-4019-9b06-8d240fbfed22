import React from 'react'
import { Empty, Button, Typography, Space } from 'antd'
import { 
  PlusOutlined, 
  SearchOutlined, 
  FileTextOutlined,
  ShoppingCartOutlined,
  HeartOutlined,
  FolderOpenOutlined
} from '@ant-design/icons'

const { Title, Paragraph } = Typography

interface EmptyStateProps {
  type?: 'search' | 'resources' | 'favorites' | 'cart' | 'collections' | 'general'
  title?: string
  description?: string
  actionText?: string
  onAction?: () => void
  showAction?: boolean
  image?: React.ReactNode
  size?: 'small' | 'default' | 'large'
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  type = 'general',
  title,
  description,
  actionText,
  onAction,
  showAction = true,
  image,
  size = 'default'
}) => {
  const getDefaultContent = () => {
    switch (type) {
      case 'search':
        return {
          title: '未找到结果',
          description: '请尝试调整搜索条件或浏览不同的分类。',
          actionText: '清除筛选',
          icon: <SearchOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
        }
      case 'resources':
        return {
          title: '暂无资源',
          description: '上传您的第一个AI资源，开始构建您的收藏。',
          actionText: '上传资源',
          icon: <FileTextOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
        }
      case 'favorites':
        return {
          title: '暂无收藏',
          description: '收藏您喜欢的资源，方便以后查找。',
          actionText: '浏览资源',
          icon: <HeartOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
        }
      case 'cart':
        return {
          title: '购物车为空',
          description: '添加一些精彩的AI资源到购物车开始购买。',
          actionText: '浏览资源',
          icon: <ShoppingCartOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
        }
      case 'collections':
        return {
          title: '暂无收藏集',
          description: '创建收藏集来整理您喜欢的资源。',
          actionText: '创建收藏集',
          icon: <FolderOpenOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
        }
      default:
        return {
          title: '这里还没有内容',
          description: '此区域为空，请稍后查看更新。',
          actionText: '刷新',
          icon: <FileTextOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
        }
    }
  }

  const defaultContent = getDefaultContent()
  const finalTitle = title || defaultContent.title
  const finalDescription = description || defaultContent.description
  const finalActionText = actionText || defaultContent.actionText
  const finalImage = image || defaultContent.icon

  const getEmptyImage = () => {
    if (size === 'small') {
      return Empty.PRESENTED_IMAGE_SIMPLE
    }
    return finalImage || Empty.PRESENTED_IMAGE_DEFAULT
  }

  return (
    <div className={`text-center py-${size === 'small' ? '8' : size === 'large' ? '20' : '12'}`}>
      <Empty
        image={getEmptyImage()}
        imageStyle={{
          height: size === 'small' ? 60 : size === 'large' ? 120 : 80,
        }}
        description={
          <Space direction="vertical" size="middle">
            <div>
              <Title 
                level={size === 'small' ? 5 : size === 'large' ? 3 : 4} 
                style={{ marginBottom: 8 }}
              >
                {finalTitle}
              </Title>
              <Paragraph 
                type="secondary" 
                style={{ 
                  marginBottom: 0,
                  fontSize: size === 'small' ? '14px' : '16px'
                }}
              >
                {finalDescription}
              </Paragraph>
            </div>
            {showAction && onAction && (
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={onAction}
                size={size === 'small' ? 'small' : size === 'large' ? 'large' : 'middle'}
              >
                {finalActionText}
              </Button>
            )}
          </Space>
        }
      />
    </div>
  )
}

// Specific empty state components for common use cases
export const SearchEmptyState: React.FC<Omit<EmptyStateProps, 'type'>> = (props) => (
  <EmptyState {...props} type="search" />
)

export const ResourcesEmptyState: React.FC<Omit<EmptyStateProps, 'type'>> = (props) => (
  <EmptyState {...props} type="resources" />
)

export const FavoritesEmptyState: React.FC<Omit<EmptyStateProps, 'type'>> = (props) => (
  <EmptyState {...props} type="favorites" />
)

export const CartEmptyState: React.FC<Omit<EmptyStateProps, 'type'>> = (props) => (
  <EmptyState {...props} type="cart" />
)

export const CollectionsEmptyState: React.FC<Omit<EmptyStateProps, 'type'>> = (props) => (
  <EmptyState {...props} type="collections" />
)
