import React, { createContext, useContext, ReactNode } from 'react'
import { notification } from 'antd'
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  InfoCircleOutlined, 
  CloseCircleOutlined 
} from '@ant-design/icons'

type NotificationType = 'success' | 'info' | 'warning' | 'error'

interface NotificationContextType {
  showNotification: (
    type: NotificationType,
    message: string,
    description?: string,
    duration?: number
  ) => void
  showSuccess: (message: string, description?: string) => void
  showError: (message: string, description?: string) => void
  showWarning: (message: string, description?: string) => void
  showInfo: (message: string, description?: string) => void
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

interface NotificationProviderProps {
  children: ReactNode
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  // Configure notification globally
  notification.config({
    placement: 'topRight',
    duration: 4.5,
    maxCount: 3,
  })

  const showNotification = (
    type: NotificationType,
    message: string,
    description?: string,
    duration?: number
  ) => {
    const icons = {
      success: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      error: <CloseCircleOutlined style={{ color: '#ff4d4f' }} />,
      warning: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
      info: <InfoCircleOutlined style={{ color: '#1890ff' }} />
    }

    notification[type]({
      message,
      description,
      icon: icons[type],
      duration: duration || 4.5,
      style: {
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      }
    })
  }

  const showSuccess = (message: string, description?: string) => {
    showNotification('success', message, description)
  }

  const showError = (message: string, description?: string) => {
    showNotification('error', message, description, 6) // Longer duration for errors
  }

  const showWarning = (message: string, description?: string) => {
    showNotification('warning', message, description)
  }

  const showInfo = (message: string, description?: string) => {
    showNotification('info', message, description)
  }

  const value: NotificationContextType = {
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo
  }

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  )
}

export const useNotification = (): NotificationContextType => {
  const context = useContext(NotificationContext)
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider')
  }
  return context
}
