import React from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Layout, Menu, Button, Avatar, Dropdown, Space, Typography } from 'antd'
import {
  HomeOutlined,
  AppstoreOutlined,
  DashboardOutlined,
  UserOutlined,
  LogoutOutlined,
  LoginOutlined,
  UserAddOutlined
} from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import { useAuth } from '@/hooks/useAuth'
import { MobileMenu } from '../MobileMenu'
import LanguageSwitcher from '../LanguageSwitcher'

const { Header: AntHeader } = Layout
const { Text } = Typography

export const Header: React.FC = () => {
  const { isAuthenticated, user, logout } = useAuth()
  const navigate = useNavigate()
  const { t } = useTranslation()

  // 简单的语言切换函数
  const toggleLanguage = () => {
    const currentLang = localStorage.getItem('language') || 'zh-CN'
    const newLang = currentLang === 'zh-CN' ? 'en-US' : 'zh-CN'
    localStorage.setItem('language', newLang)
    window.location.reload() // 简单的页面刷新来应用新语言
  }

  const handleLogout = () => {
    logout()
    navigate('/')
  }

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: t('nav.profile'),
      onClick: () => navigate('/profile')
    },
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: t('nav.dashboard'),
      onClick: () => navigate('/dashboard')
    },
    {
      type: 'divider' as const
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: t('nav.logout'),
      onClick: handleLogout
    }
  ]

  const menuItems = [
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: <Link to="/">{t('nav.home')}</Link>
    },
    {
      key: 'resources',
      icon: <AppstoreOutlined />,
      label: <Link to="/resources">{t('nav.resources')}</Link>
    },
    {
      key: 'categories',
      label: <Link to="/categories">{t('nav.categories')}</Link>
    }
  ]

  if (isAuthenticated) {
    menuItems.push({
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: <Link to="/dashboard">{t('nav.dashboard')}</Link>
    })
  }

  return (
    <AntHeader className="bg-white shadow-sm border-b border-gray-200 px-0">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center h-full">
        {/* Mobile Menu */}
        <div className="md:hidden">
          <MobileMenu />
        </div>

        {/* Logo */}
        <div className="flex items-center">
          <Link to="/" className="flex items-center">
            <Text strong className="text-xl text-gray-900">
              🤖 AIGC Service Hub
            </Text>
          </Link>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden md:flex flex-1 max-w-2xl mx-8">
          <Menu
            mode="horizontal"
            items={menuItems}
            className="border-none bg-transparent"
            style={{ lineHeight: '64px' }}
          />
        </div>

        {/* Desktop User menu */}
        <div className="hidden md:flex items-center space-x-4">
          {isAuthenticated ? (
            <Space>
              <Button
                type="default"
                style={{ backgroundColor: '#f0f0f0', border: '1px solid #d9d9d9' }}
                onClick={toggleLanguage}
                title="切换语言 / Switch Language"
              >
                🌐 中/EN
              </Button>
              <LanguageSwitcher />
              <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
                <Space className="cursor-pointer">
                  <Avatar
                    src={user?.avatarUrl}
                    icon={<UserOutlined />}
                    size="default"
                  />
                  <Text>{user?.username}</Text>
                </Space>
              </Dropdown>
            </Space>
          ) : (
            <Space>
              <Button
                type="default"
                style={{ backgroundColor: '#f0f0f0', border: '1px solid #d9d9d9' }}
                onClick={toggleLanguage}
                title="切换语言 / Switch Language"
              >
                🌐 中/EN
              </Button>
              <LanguageSwitcher />
              <Button
                icon={<LoginOutlined />}
                onClick={() => navigate('/login')}
              >
                {t('nav.login')}
              </Button>
              <Button
                type="primary"
                icon={<UserAddOutlined />}
                onClick={() => navigate('/register')}
              >
                {t('nav.register')}
              </Button>
            </Space>
          )}
        </div>

        {/* Mobile User Avatar */}
        {isAuthenticated && (
          <div className="md:hidden">
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <Avatar
                src={user?.avatarUrl}
                icon={<UserOutlined />}
                size="default"
                className="cursor-pointer"
              />
            </Dropdown>
          </div>
        )}
      </div>
    </AntHeader>
  )
}
