import AWS from 'aws-sdk';
import multer from 'multer';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';

// Configure AWS
AWS.config.update({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION || 'us-west-2'
});

const s3 = new AWS.S3();
const bucketName = process.env.AWS_S3_BUCKET || 'aigc-service-hub-files';

export interface UploadResult {
  key: string;
  url: string;
  size: number;
  mimeType: string;
}

export class StorageService {
  static async uploadFile(
    file: Express.Multer.File,
    folder: string = 'uploads'
  ): Promise<UploadResult> {
    try {
      const fileExtension = path.extname(file.originalname);
      const fileName = `${uuidv4()}${fileExtension}`;
      const key = `${folder}/${fileName}`;

      const uploadParams = {
        Bucket: bucketName,
        Key: key,
        Body: file.buffer,
        ContentType: file.mimetype,
        ACL: 'public-read',
        Metadata: {
          originalName: file.originalname,
          uploadedAt: new Date().toISOString()
        }
      };

      const result = await s3.upload(uploadParams).promise();

      return {
        key,
        url: result.Location,
        size: file.size,
        mimeType: file.mimetype
      };
    } catch (error) {
      console.error('File upload error:', error);
      throw new Error('Failed to upload file to S3');
    }
  }

  static async uploadMultipleFiles(
    files: Express.Multer.File[],
    folder: string = 'uploads'
  ): Promise<UploadResult[]> {
    try {
      const uploadPromises = files.map(file => this.uploadFile(file, folder));
      return await Promise.all(uploadPromises);
    } catch (error) {
      console.error('Multiple file upload error:', error);
      throw new Error('Failed to upload files to S3');
    }
  }

  static async deleteFile(key: string): Promise<boolean> {
    try {
      const deleteParams = {
        Bucket: bucketName,
        Key: key
      };

      await s3.deleteObject(deleteParams).promise();
      return true;
    } catch (error) {
      console.error('File deletion error:', error);
      return false;
    }
  }

  static async deleteMultipleFiles(keys: string[]): Promise<boolean> {
    try {
      const deleteParams = {
        Bucket: bucketName,
        Delete: {
          Objects: keys.map(key => ({ Key: key }))
        }
      };

      await s3.deleteObjects(deleteParams).promise();
      return true;
    } catch (error) {
      console.error('Multiple file deletion error:', error);
      return false;
    }
  }

  static async getSignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
    try {
      const params = {
        Bucket: bucketName,
        Key: key,
        Expires: expiresIn
      };

      return s3.getSignedUrl('getObject', params);
    } catch (error) {
      console.error('Get signed URL error:', error);
      throw new Error('Failed to generate signed URL');
    }
  }

  static async copyFile(sourceKey: string, destinationKey: string): Promise<boolean> {
    try {
      const copyParams = {
        Bucket: bucketName,
        CopySource: `${bucketName}/${sourceKey}`,
        Key: destinationKey
      };

      await s3.copyObject(copyParams).promise();
      return true;
    } catch (error) {
      console.error('File copy error:', error);
      return false;
    }
  }

  static getFileUrl(key: string): string {
    return `https://${bucketName}.s3.${process.env.AWS_REGION || 'us-west-2'}.amazonaws.com/${key}`;
  }

  static validateFileType(file: Express.Multer.File, allowedTypes: string[]): boolean {
    return allowedTypes.includes(file.mimetype);
  }

  static validateFileSize(file: Express.Multer.File, maxSizeInBytes: number): boolean {
    return file.size <= maxSizeInBytes;
  }
}

// Multer configuration for different file types
export const createMulterConfig = (
  allowedTypes: string[],
  maxSize: number,
  fieldName: string = 'file'
) => {
  return multer({
    storage: multer.memoryStorage(),
    limits: {
      fileSize: maxSize
    },
    fileFilter: (req, file, cb) => {
      if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`));
      }
    }
  });
};

// Predefined configurations
export const avatarUpload = createMulterConfig(
  ['image/jpeg', 'image/png', 'image/gif'],
  5 * 1024 * 1024, // 5MB
  'avatar'
);

export const thumbnailUpload = createMulterConfig(
  ['image/jpeg', 'image/png', 'image/gif'],
  2 * 1024 * 1024, // 2MB
  'thumbnail'
);

export const resourceFileUpload = createMulterConfig(
  [
    'application/zip',
    'application/x-zip-compressed',
    'application/gzip',
    'application/x-tar',
    'application/json',
    'text/plain',
    'text/markdown',
    'application/octet-stream'
  ],
  100 * 1024 * 1024, // 100MB
  'resourceFile'
);

export const documentUpload = createMulterConfig(
  [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'text/markdown'
  ],
  10 * 1024 * 1024, // 10MB
  'document'
);
