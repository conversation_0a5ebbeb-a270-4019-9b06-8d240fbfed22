import React from 'react'
import { Tag, Space, Tooltip } from 'antd'
import { Link } from 'react-router-dom'

export const StyleBar: React.FC = () => {
  const styles = [
    { name: '电商', desc: '电商产品展示和营销素材' },
    { name: '网页', desc: '网页设计和UI界面元素' },
    { name: '写真', desc: '人像写真和肖像摄影风格' },
    { name: '节日', desc: '节日庆典和特殊场合主题' },
    { name: '动漫', desc: '动漫卡通和二次元风格' },
    { name: '国画', desc: '中国传统绘画艺术风格' },
    { name: '建筑', desc: '建筑设计和空间艺术' },
    { name: '园林', desc: '园林景观和自然环境' },
    { name: '摄影', desc: '专业摄影和视觉艺术' },
    { name: '卡通', desc: '卡通插画和可爱风格' },
    { name: '人像', desc: '人物肖像和表情捕捉' },
    { name: '老照片', desc: '复古怀旧和历史风格' },
    { name: '美女', desc: '女性美学和时尚风格' },
    { name: '男人', desc: '男性形象和阳刚风格' },
    { name: '女人', desc: '女性魅力和优雅风格' },
    { name: '素材', desc: '通用设计素材和元素' },
    { name: '科技', desc: '科技感和未来主义风格' },
    { name: '商务', desc: '商务办公和专业形象' },
    { name: '艺术', desc: '艺术创作和抽象表现' },
    { name: '时尚', desc: '时尚潮流和流行元素' },
    { name: '自然', desc: '自然风光和生态环境' },
    { name: '抽象', desc: '抽象艺术和概念表达' }
  ]

  return (
    <div style={{
      height: '40px',
      backgroundColor: '#2a2a2a',
      borderBottom: '1px solid #3a3a3a',
      display: 'flex',
      alignItems: 'center',
      padding: '0 24px',
      overflow: 'hidden'
    }}>
      <div style={{ 
        display: 'flex', 
        alignItems: 'center',
        width: '100%',
        overflow: 'hidden'
      }}>
        <span style={{
          color: '#888',
          marginRight: '12px',
          fontSize: '13px',
          whiteSpace: 'nowrap'
        }}>
          风格:
        </span>
        <div style={{ 
          display: 'flex',
          gap: '6px',
          overflow: 'auto',
          scrollbarWidth: 'none',
          msOverflowStyle: 'none'
        }}>
          {styles.map(style => (
            <Tooltip key={style.name} title={style.desc} placement="bottom">
              <Link to={`/resources?style=${encodeURIComponent(style.name)}`}>
                <Tag
                  style={{
                    cursor: 'pointer',
                    border: '1px solid #4a4a4a',
                    backgroundColor: '#3a3a3a',
                    color: '#ccc',
                    fontSize: '11px',
                    padding: '2px 8px',
                    margin: 0,
                    whiteSpace: 'nowrap',
                    borderRadius: '12px',
                    transition: 'all 0.2s ease'
                  }}
                  className="style-tag hover:bg-purple-600 hover:border-purple-500 hover:text-white"
                >
                  {style.name}
                </Tag>
              </Link>
            </Tooltip>
          ))}
        </div>
      </div>
    </div>
  )
}
