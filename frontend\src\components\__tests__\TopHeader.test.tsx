import { describe, it, expect, vi } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import { TopHeader } from '../Layout/TopHeader'
import { render } from '@/test/test-utils'

// Mock useAuth hook
vi.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({
    isAuthenticated: false,
    user: null,
    login: vi.fn(),
    logout: vi.fn(),
    loading: false,
    error: null
  })
}))

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    Link: ({ children, to }: { children: React.ReactNode; to: string }) => (
      <a href={to}>{children}</a>
    )
  }
})

describe('TopHeader', () => {
  it('renders logo', () => {
    render(<TopHeader />)
    
    const logo = screen.getByAltText('AIGC Service Hub')
    expect(logo).toBeInTheDocument()
    expect(logo).toHaveAttribute('src', '/aigc-logo-optimized.svg')
  })

  it('renders search input', () => {
    render(<TopHeader />)
    
    const searchInput = screen.getByPlaceholderText('搜索AI资源...')
    expect(searchInput).toBeInTheDocument()
  })

  it('renders publish dropdown button', () => {
    render(<TopHeader />)
    
    const publishButton = screen.getByText('发布')
    expect(publishButton).toBeInTheDocument()
  })

  it('renders login button when not authenticated', () => {
    render(<TopHeader />)
    
    const loginButton = screen.getByText('登录')
    expect(loginButton).toBeInTheDocument()
  })

  it('renders share button', () => {
    render(<TopHeader />)
    
    const shareButton = screen.getByText('分享')
    expect(shareButton).toBeInTheDocument()
  })

  it('renders language switcher', () => {
    render(<TopHeader />)
    
    // Language switcher should be present
    const languageElements = screen.getAllByText(/CN|US/)
    expect(languageElements.length).toBeGreaterThan(0)
  })

  it('handles logo hover effects', () => {
    render(<TopHeader />)
    
    const logo = screen.getByAltText('AIGC Service Hub')
    
    // Test hover effect
    fireEvent.mouseEnter(logo)
    expect(logo).toHaveStyle('transform: translateY(12px) scale(1.05)')
    
    fireEvent.mouseLeave(logo)
    expect(logo).toHaveStyle('transform: translateY(12px) scale(1)')
  })

  it('handles search input changes', () => {
    render(<TopHeader />)
    
    const searchInput = screen.getByPlaceholderText('搜索AI资源...')
    
    fireEvent.change(searchInput, { target: { value: 'test search' } })
    expect(searchInput).toHaveValue('test search')
  })

  it('opens publish dropdown on click', () => {
    render(<TopHeader />)
    
    const publishButton = screen.getByText('发布')
    fireEvent.click(publishButton)
    
    // Should show dropdown menu items
    expect(screen.getByText('LoRA模型')).toBeInTheDocument()
    expect(screen.getByText('工作流')).toBeInTheDocument()
    expect(screen.getByText('工具')).toBeInTheDocument()
    expect(screen.getByText('提示词')).toBeInTheDocument()
    expect(screen.getByText('微调模型')).toBeInTheDocument()
  })

  it('has correct responsive design classes', () => {
    render(<TopHeader />)
    
    const header = screen.getByRole('banner')
    expect(header).toHaveStyle('height: 100px')
    expect(header).toHaveStyle('background-color: #1f1f1f')
  })
})
