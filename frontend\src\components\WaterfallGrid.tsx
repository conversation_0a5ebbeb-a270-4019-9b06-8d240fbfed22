import React, { useState, useEffect } from 'react'
import { Card, Typography, Tag, Avatar, Button, Spin } from 'antd'
import { DownloadOutlined, EyeOutlined, HeartOutlined, StarOutlined, DatabaseOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import InfiniteScroll from 'react-infinite-scroll-component'

const { Text, Title } = Typography
const { Meta } = Card

interface ResourceItem {
  id: string
  title: string
  description: string
  image: string
  author: {
    id: string
    name: string
    avatar: string
    verified?: boolean
  }
  type: string
  price: number
  downloads: number
  likes: number
  views: number
  tags: string[]
  createdAt: string
  featured?: boolean
}

export const WaterfallGrid: React.FC = () => {
  const navigate = useNavigate()
  const [resources, setResources] = useState<ResourceItem[]>([])
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)

  // 模拟数据
  const generateMockData = (pageNum: number): ResourceItem[] => {
    const types = ['LoRA', '工作流', '工具', '提示词', '微调模型']
    const tags = ['AI绘画', '人像', '风景', '动漫', '写实', '抽象', '商业', '艺术']
    const authors = [
      { id: 'author-1', name: 'AI大师', avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face' },
      { id: 'author-2', name: '创意工坊', avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face' },
      { id: 'author-3', name: '模型专家', avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face' },
      { id: 'author-4', name: '设计师', avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face' }
    ]

    return Array.from({ length: 20 }, (_, i) => {
      const id = `${pageNum}-${i}`
      const randomHeight = Math.floor(Math.random() * 200) + 200
      return {
        id,
        title: `AI创作作品 ${id}`,
        description: '这是一个精美的AI生成作品，展现了人工智能在创意领域的无限可能...',
        image: `https://picsum.photos/300/${randomHeight}?random=${id}`,
        author: authors[Math.floor(Math.random() * authors.length)],
        type: types[Math.floor(Math.random() * types.length)],
        price: Math.random() > 0.3 ? Math.floor(Math.random() * 50) + 5 : 0,
        downloads: Math.floor(Math.random() * 5000) + 100,
        likes: Math.floor(Math.random() * 1000) + 50,
        views: Math.floor(Math.random() * 10000) + 500,
        tags: tags.slice(0, Math.floor(Math.random() * 3) + 1),
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        featured: Math.random() > 0.8
      }
    })
  }

  const loadMoreData = () => {
    if (loading) return
    
    setLoading(true)
    // 模拟API调用延迟
    setTimeout(() => {
      const newData = generateMockData(page)
      setResources(prev => [...prev, ...newData])
      setPage(prev => prev + 1)
      setLoading(false)
      
      // 模拟数据加载完毕
      if (page >= 5) {
        setHasMore(false)
      }
    }, 1000)
  }

  useEffect(() => {
    loadMoreData()
  }, [])

  // 导航到创作者页面
  const handleCreatorClick = (creatorId: string) => {
    navigate(`/creator/${creatorId}`)
  }

  // 导航到资源详情页面
  const handleResourceClick = (resourceId: string) => {
    navigate(`/resource/${resourceId}`)
  }

  const ResourceCard: React.FC<{ resource: ResourceItem }> = ({ resource }) => (
    <div
      className="waterfall-resource-card"
      style={{
        marginBottom: '16px',
        borderRadius: '12px',
        overflow: 'hidden',
        backgroundColor: '#2a2a2a',
        border: '1px solid transparent',
        borderColor: '#3a3a3a',
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        display: 'flex',
        flexDirection: 'column'
      }}
      onClick={() => handleResourceClick(resource.id)}
    >
      {/* 图片区域 - 占75%高度 */}
      <div
        style={{
          position: 'relative',
          flex: '3',
          minHeight: '200px',
          overflow: 'hidden'
        }}
      >
        <img
          alt={resource.title}
          src={resource.image}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            display: 'block'
          }}
        />

        {/* 精选标签 */}
        {resource.featured && (
          <div style={{
            position: 'absolute',
            top: '8px',
            left: '8px',
            background: 'linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%)',
            color: '#fff',
            padding: '4px 8px',
            borderRadius: '6px',
            fontSize: '12px',
            fontWeight: 'bold',
            boxShadow: '0 2px 8px rgba(139, 92, 246, 0.3)'
          }}>
            <StarOutlined style={{ marginRight: '4px' }} />
            精选
          </div>
        )}

        {/* 类型标签 */}
        <div style={{
          position: 'absolute',
          top: '8px',
          right: '8px',
          background: 'linear-gradient(135deg, rgba(42, 42, 42, 0.9), rgba(58, 58, 58, 0.9))',
          color: '#fff',
          padding: '4px 8px',
          borderRadius: '4px',
          fontSize: '12px'
        }}>
          {resource.type}
        </div>

        {/* 免费标签 */}
        {resource.price === 0 && (
          <div style={{
            position: 'absolute',
            bottom: '8px',
            left: '8px',
            background: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
            color: '#fff',
            padding: '4px 8px',
            borderRadius: '6px',
            fontSize: '12px',
            fontWeight: 'bold'
          }}>
            免费
          </div>
        )}
      </div>

      {/* 内容区域 - 占25%高度 */}
      <div
        style={{
          flex: '1',
          padding: '12px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between'
        }}
      >
        {/* 标题和作者信息 */}
        <div style={{ marginBottom: '8px' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            marginBottom: '6px'
          }}>
            <Avatar
              src={resource.author.avatar}
              size={24}
              style={{
                marginRight: '8px',
                cursor: 'pointer',
                transition: 'transform 0.2s ease'
              }}
              className="creator-avatar"
              onClick={(e) => {
                e.stopPropagation()
                handleCreatorClick(resource.author.id)
              }}
            />
            <div style={{ flex: 1, minWidth: 0 }}>
              <div style={{
                fontSize: '13px',
                fontWeight: 'bold',
                color: '#fff',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {resource.title}
              </div>
              <div style={{
                fontSize: '10px',
                color: '#888'
              }}>
                by {resource.author.name}
              </div>
            </div>
          </div>
        </div>

        {/* 标签 */}
        <div style={{ marginBottom: '8px' }}>
          {resource.tags.slice(0, 2).map(tag => (
            <Tag key={tag} size="small" style={{
              fontSize: '9px',
              margin: '1px 2px',
              backgroundColor: '#3a3a3a',
              borderColor: '#4a4a4a',
              color: '#ccc'
            }}>
              {tag}
            </Tag>
          ))}
        </div>

        {/* 统计信息 */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-around',
          fontSize: '11px',
          color: '#888',
          borderTop: '1px solid #3a3a3a',
          paddingTop: '6px'
        }}>
          <span><EyeOutlined /> {(resource.views / 1000).toFixed(1)}k</span>
          <span><DownloadOutlined /> {resource.downloads}</span>
          <span><HeartOutlined /> {resource.likes}</span>
        </div>
      </div>
    </div>
  )

  return (
    <div style={{ padding: '24px', backgroundColor: '#1a1a1a' }}>
      {/* 标题区域 */}
      <div style={{ textAlign: 'left', marginBottom: '24px' }}>
        <Title level={3} style={{ margin: 0, color: '#fff', fontSize: '20px' }}>
          <DatabaseOutlined style={{ color: '#8B5CF6', marginRight: '8px', fontSize: '20px' }} />
          所有资源
        </Title>
        <Text style={{ color: '#888' }}>随时由创作者上传更新</Text>
      </div>

      <InfiniteScroll
        dataLength={resources.length}
        next={loadMoreData}
        hasMore={hasMore}
        loader={
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin size="large" />
          </div>
        }
        endMessage={
          <div style={{ textAlign: 'center', padding: '20px', color: '#888' }}>
            已加载全部内容
          </div>
        }
      >
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
          gap: '16px'
        }}>
          {resources.map(resource => (
            <ResourceCard key={resource.id} resource={resource} />
          ))}
        </div>
      </InfiniteScroll>
    </div>
  )
}
