import React from 'react'
import { Layout as AntLayout, BackTop } from 'antd'
import { Header } from './Header'
import { Footer } from './Footer'

const { Content } = AntLayout

interface LayoutProps {
  children: React.ReactNode
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <AntLayout className="min-h-screen">
      <Header />
      <Content className="bg-gray-50">
        {children}
      </Content>
      <Footer />
      <BackTop />
    </AntLayout>
  )
}
