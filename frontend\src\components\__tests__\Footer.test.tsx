import { describe, it, expect, vi } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import { Footer } from '../Layout/Footer'
import { render } from '@/test/test-utils'

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => vi.fn()
  }
})

describe('Footer', () => {
  it('renders footer with correct height and background', () => {
    render(<Footer />)
    
    const footer = document.querySelector('.ant-layout-footer')
    expect(footer).toBeInTheDocument()
    expect(footer).toHaveClass('footer')
  })

  it('displays logo', () => {
    render(<Footer />)
    
    const logo = screen.getByAltText('AIGC Service Hub')
    expect(logo).toBeInTheDocument()
    expect(logo).toHaveAttribute('src', '/aigc-logo-optimized.svg')
  })

  it('displays platform description', () => {
    render(<Footer />)
    
    const description = screen.getByText(/AIGC Service Hub 是一个致力于AI资源交易创收的创新平台/)
    expect(description).toBeInTheDocument()
  })

  it('renders social media section title', () => {
    render(<Footer />)
    
    expect(screen.getByText('请关注 AIGC Service Hub')).toBeInTheDocument()
  })

  it('displays social media links', () => {
    render(<Footer />)
    
    // Check for social media icons
    const youtubeIcon = document.querySelector('.anticon-youtube')
    const twitterIcon = document.querySelector('.anticon-twitter')
    const linkedinIcon = document.querySelector('.anticon-linkedin')
    const githubIcon = document.querySelector('.anticon-github')
    const discordIcon = document.querySelector('.anticon-message')
    
    expect(youtubeIcon).toBeInTheDocument()
    expect(twitterIcon).toBeInTheDocument()
    expect(linkedinIcon).toBeInTheDocument()
    expect(githubIcon).toBeInTheDocument()
    expect(discordIcon).toBeInTheDocument()
  })

  it('renders about section title', () => {
    render(<Footer />)
    
    expect(screen.getByText('关于 AIGC Service Hub')).toBeInTheDocument()
  })

  it('displays about links', () => {
    render(<Footer />)
    
    expect(screen.getByText('招贤纳士')).toBeInTheDocument()
    expect(screen.getByText('资讯及活动中心')).toBeInTheDocument()
    expect(screen.getByText('技术支持与交流')).toBeInTheDocument()
    expect(screen.getByText('帮助中心')).toBeInTheDocument()
  })

  it('displays legal links', () => {
    render(<Footer />)
    
    expect(screen.getByText('与 AIGC Service Hub 联系')).toBeInTheDocument()
    expect(screen.getByText('用户协议')).toBeInTheDocument()
    expect(screen.getByText('隐私条款')).toBeInTheDocument()
    expect(screen.getByText('使用条款')).toBeInTheDocument()
    expect(screen.getByText('商标')).toBeInTheDocument()
    expect(screen.getByText('关于我们的广告')).toBeInTheDocument()
  })

  it('displays registration information', () => {
    render(<Footer />)
    
    expect(screen.getByText(/津ICP备2025029783号/)).toBeInTheDocument()
    expect(screen.getByText(/生成式人工智能服务管理暂行办法备案号/)).toBeInTheDocument()
  })

  it('displays copyright information', () => {
    render(<Footer />)
    
    expect(screen.getByText('© AIGC Service Hub 2025')).toBeInTheDocument()
  })

  it('handles logo click navigation', () => {
    const mockNavigate = vi.fn()
    vi.mocked(require('react-router-dom').useNavigate).mockReturnValue(mockNavigate)
    
    render(<Footer />)
    
    const logo = screen.getByAltText('AIGC Service Hub')
    fireEvent.click(logo)
    expect(mockNavigate).toHaveBeenCalledWith('/')
  })

  it('handles about link clicks', () => {
    const mockNavigate = vi.fn()
    vi.mocked(require('react-router-dom').useNavigate).mockReturnValue(mockNavigate)
    
    render(<Footer />)
    
    const careersLink = screen.getByText('招贤纳士')
    fireEvent.click(careersLink)
    expect(mockNavigate).toHaveBeenCalledWith('/careers')
    
    const newsLink = screen.getByText('资讯及活动中心')
    fireEvent.click(newsLink)
    expect(mockNavigate).toHaveBeenCalledWith('/news')
  })

  it('handles legal link clicks', () => {
    const mockNavigate = vi.fn()
    vi.mocked(require('react-router-dom').useNavigate).mockReturnValue(mockNavigate)
    
    render(<Footer />)
    
    const contactLink = screen.getByText('与 AIGC Service Hub 联系')
    fireEvent.click(contactLink)
    expect(mockNavigate).toHaveBeenCalledWith('/contact')
    
    const termsLink = screen.getByText('用户协议')
    fireEvent.click(termsLink)
    expect(mockNavigate).toHaveBeenCalledWith('/terms')
  })

  it('applies hover effects to social links', () => {
    render(<Footer />)
    
    const socialLinks = document.querySelectorAll('.socialLink')
    expect(socialLinks.length).toBeGreaterThan(0)
    
    // Test hover effect on first social link
    if (socialLinks[0]) {
      fireEvent.mouseEnter(socialLinks[0])
      expect(socialLinks[0]).toHaveClass('socialLink')
      
      fireEvent.mouseLeave(socialLinks[0])
      expect(socialLinks[0]).toHaveClass('socialLink')
    }
  })

  it('applies hover effects to about links', () => {
    render(<Footer />)
    
    const aboutLinks = document.querySelectorAll('.aboutLink')
    expect(aboutLinks.length).toBeGreaterThan(0)
  })

  it('applies hover effects to legal links', () => {
    render(<Footer />)
    
    const legalLinks = document.querySelectorAll('.legalLink')
    expect(legalLinks.length).toBeGreaterThan(0)
  })

  it('has proper responsive layout structure', () => {
    render(<Footer />)
    
    // Check for Ant Design grid structure
    const cols = document.querySelectorAll('.ant-col')
    expect(cols.length).toBeGreaterThan(0)
  })

  it('displays divider before legal section', () => {
    render(<Footer />)
    
    const divider = document.querySelector('.ant-divider')
    expect(divider).toBeInTheDocument()
  })

  it('has correct CSS module classes applied', () => {
    render(<Footer />)
    
    const footer = document.querySelector('.footer')
    expect(footer).toBeInTheDocument()
    
    const footerContainer = document.querySelector('.footerContainer')
    expect(footerContainer).toBeInTheDocument()
  })
})
