{"name": "aigc-service-hub-backend", "version": "1.0.0", "description": "Backend API for AIGC Service Hub - AI Resource Trading Platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/simple-server.ts", "dev:watch": "nodemon --watch src --ext ts --exec ts-node src/server.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "migrate": "knex migrate:latest", "migrate:rollback": "knex migrate:rollback", "seed": "knex seed:run", "db:reset": "knex migrate:rollback --all && knex migrate:latest && knex seed:run"}, "keywords": ["ai", "aigc", "trading", "platform", "api", "nodejs", "typescript"], "author": "AIGC-OFFICE", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.1", "multer": "^1.4.5-lts.1", "pg": "^8.11.1", "redis": "^4.6.7", "knex": "^2.5.1", "aws-sdk": "^2.1419.0", "paypal-rest-sdk": "^1.8.1", "nodemailer": "^6.9.3", "winston": "^3.10.0", "joi": "^17.9.2", "uuid": "^9.0.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^20.4.2", "@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/compression": "^1.7.2", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/multer": "^1.4.7", "@types/pg": "^8.10.2", "@types/uuid": "^9.0.2", "@types/jest": "^29.5.3", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.1.0", "@typescript-eslint/parser": "^6.1.0", "eslint": "^8.45.0", "prettier": "^3.0.0", "typescript": "^5.1.6", "ts-node": "^10.9.1", "nodemon": "^3.0.1", "jest": "^29.6.1", "ts-jest": "^29.1.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}