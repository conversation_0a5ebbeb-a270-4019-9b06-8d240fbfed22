import axios, { AxiosInstance, AxiosResponse } from 'axios'

// API Response interface
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  message?: string
  timestamp: string
}

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle errors
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized - redirect to login
      localStorage.removeItem('authToken')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authApi = {
  register: (data: {
    email: string
    password: string
    username: string
    fullName: string
    userType?: string
  }) => api.post<ApiResponse>('/auth/register', data),

  login: (data: { email: string; password: string }) =>
    api.post<ApiResponse>('/auth/login', data),

  logout: () => api.post<ApiResponse>('/auth/logout'),

  getProfile: () => api.get<ApiResponse>('/auth/profile'),

  updateProfile: (data: any) => api.put<ApiResponse>('/auth/profile', data),
}

// Resources API
export const resourcesApi = {
  getResources: (params?: any) =>
    api.get<ApiResponse>('/resources', { params }),

  getResource: (id: string) => api.get<ApiResponse>(`/resources/${id}`),

  createResource: (data: any) => api.post<ApiResponse>('/resources', data),

  updateResource: (id: string, data: any) =>
    api.put<ApiResponse>(`/resources/${id}`, data),

  deleteResource: (id: string) => api.delete<ApiResponse>(`/resources/${id}`),
}

// Categories API
export const categoriesApi = {
  getCategories: () => api.get<ApiResponse>('/categories'),
}

// Users API
export const usersApi = {
  getUser: (id: string) => api.get<ApiResponse>(`/users/${id}`),
  updateUserProfile: (id: string, data: any) =>
    api.put<ApiResponse>(`/users/${id}/profile`, data),
}

// Transactions API
export const transactionsApi = {
  getTransactions: () => api.get<ApiResponse>('/transactions'),
  createTransaction: (data: any) => api.post<ApiResponse>('/transactions', data),
  getTransaction: (id: string) => api.get<ApiResponse>(`/transactions/${id}`),
}

// Search API
export const searchApi = {
  searchResources: (params: any) =>
    api.get<ApiResponse>('/search/resources', { params }),
}

export default api
