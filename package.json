{"dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/pro-components": "^2.8.9", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.11", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^2.0.0", "@types/pg": "^8.15.4", "@vitejs/plugin-react": "^4.6.0", "antd": "^5.26.4", "autoprefixer": "^10.4.21", "aws-sdk": "^2.1692.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.8.0", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^17.1.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "nodemon": "^3.1.10", "paypal-rest-sdk": "^1.8.1", "pg": "^8.16.3", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.6.0", "react-infinite-scroll-component": "^6.1.0", "react-router-dom": "^7.6.3", "tailwindcss": "^4.1.11", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^7.0.3", "zustand": "^5.0.6"}}