import { Router } from 'express';
import { body, query } from 'express-validator';
import { authMiddleware, optionalAuth } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';
import { resourceController } from '../controllers/resourceController';

const router = Router();

// Get all resources (public)
router.get(
  '/',
  optionalAuth,
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('minPrice').optional().isFloat({ min: 0 }),
    query('maxPrice').optional().isFloat({ min: 0 }),
    query('sortBy').optional().isIn(['created_at', 'updated_at', 'price', 'rating_average', 'download_count', 'title']),
    query('sortOrder').optional().isIn(['ASC', 'DESC'])
  ],
  validateRequest,
  resourceController.getResources
);

// Create new resource (requires auth)
router.post(
  '/',
  authMiddleware,
  [
    body('title').isLength({ min: 3, max: 255 }).trim(),
    body('description').isLength({ min: 10, max: 2000 }).trim(),
    body('resourceType').isIn(['fine_tuned_model', 'lora', 'workflow', 'prompt', 'tool']),
    body('categoryId').isUUID(),
    body('price').isFloat({ min: 0 }),
    body('currency').optional().isLength({ min: 3, max: 3 }),
    body('tags').optional().isArray()
  ],
  validateRequest,
  resourceController.createResource
);

// Get my resources (requires auth)
router.get(
  '/my',
  authMiddleware,
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 })
  ],
  validateRequest,
  resourceController.getMyResources
);

// Get specific resource (public)
router.get('/:id', optionalAuth, resourceController.getResource);

// Update resource (requires auth + ownership)
router.put(
  '/:id',
  authMiddleware,
  [
    body('title').optional().isLength({ min: 3, max: 255 }).trim(),
    body('description').optional().isLength({ min: 10, max: 2000 }).trim(),
    body('categoryId').optional().isUUID(),
    body('price').optional().isFloat({ min: 0 }),
    body('tags').optional().isArray(),
    body('thumbnailUrl').optional().isURL()
  ],
  validateRequest,
  resourceController.updateResource
);

// Delete resource (requires auth + ownership)
router.delete('/:id', authMiddleware, resourceController.deleteResource);

export default router;