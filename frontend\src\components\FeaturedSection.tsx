import React from 'react'
import { Row, Col, Typography, Avatar } from 'antd'
import { StarOutlined, DownloadOutlined, EyeOutlined, HeartOutlined, CrownOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

const { Title, Text } = Typography

export const FeaturedSection: React.FC = () => {
  const navigate = useNavigate()

  const featuredWorks = [
    {
      id: 1,
      resourceId: 'ai-portrait-master-lora',
      title: 'AI写真大师模型',
      image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=240&fit=crop',
      author: {
        id: 'ai-master',
        name: 'AI大师',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
        type: '个人创作者'
      },
      type: 'LoRA',
      downloads: 1520,
      likes: 340,
      views: 8900
    },
    {
      id: 2,
      resourceId: 'business-poster-workflow',
      title: '商业海报生成工作流',
      image: 'https://images.unsplash.com/photo-1676299081847-824916de030a?w=400&h=240&fit=crop',
      author: {
        id: 'creative-workshop',
        name: '创意工坊',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face',
        type: '企业创作者'
      },
      type: '工作流',
      downloads: 2340,
      likes: 567,
      views: 12400
    },
    {
      id: 3,
      resourceId: 'smart-copywriting-tool',
      title: '智能文案生成器',
      image: 'https://images.unsplash.com/photo-1686191128892-c0c8b8e5e3b8?w=400&h=240&fit=crop',
      author: {
        id: 'copywriter-expert',
        name: '文案专家',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
        type: '个人创作者'
      },
      type: '工具',
      downloads: 890,
      likes: 234,
      views: 5600
    }
  ]

  // 处理创作者点击事件
  const handleCreatorClick = (creatorId: string) => {
    navigate(`/creator/${creatorId}`)
  }

  // 处理卡片点击事件
  const handleCardClick = (resourceId: string) => {
    navigate(`/resource/${resourceId}`)
  }

  return (
    <div style={{ padding: '24px', backgroundColor: '#1a1a1a' }}>
      <div style={{ textAlign: 'left', marginBottom: '24px' }}>
        <Title level={3} style={{ margin: 0, color: '#fff', fontSize: '20px' }}>
          <StarOutlined style={{ color: '#8B5CF6', marginRight: '8px', fontSize: '20px' }} />
          精选作品
        </Title>
        <Text style={{ color: '#888' }}>管理员精心挑选的优质AI资源</Text>
      </div>

      <Row gutter={[16, 16]}>
        {featuredWorks.map(work => (
          <Col key={work.id} xs={24} sm={24} md={8} lg={8} xl={8}>
            <div
              style={{
                backgroundColor: '#2a2a2a',
                borderColor: '#3a3a3a',
                border: '1px solid #3a3a3a',
                borderRadius: '12px',
                overflow: 'hidden',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                height: '100%',
                display: 'flex',
                flexDirection: 'column'
              }}
              className="featured-work-card"
              onClick={() => handleCardClick(work.resourceId)}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)'
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(139, 92, 246, 0.15)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = 'none'
              }}
            >
              {/* 图片区域 - 占75% (3/4) */}
              <div style={{
                position: 'relative',
                width: '100%',
                height: '240px', // 增加高度以保持3:1比例
                backgroundColor: '#3a3a3a',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column',
                backgroundImage: 'linear-gradient(45deg, #2a2a2a 25%, transparent 25%), linear-gradient(-45deg, #2a2a2a 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #2a2a2a 75%), linear-gradient(-45deg, transparent 75%, #2a2a2a 75%)',
                backgroundSize: '20px 20px',
                backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
              }}>
                {/* 占位符内容 */}
                <div style={{
                  textAlign: 'center',
                  color: '#888',
                  padding: '20px'
                }}>
                  <div style={{
                    fontSize: '48px',
                    marginBottom: '12px',
                    color: '#666'
                  }}>
                    📷
                  </div>
                  <div style={{
                    fontSize: '14px',
                    fontWeight: 'bold',
                    marginBottom: '8px',
                    color: '#aaa'
                  }}>
                    推荐尺寸 400x300px
                  </div>
                  <div style={{
                    fontSize: '12px',
                    color: '#888'
                  }}>
                    创作者上传
                  </div>
                </div>

                {/* 精选标签 - 左上角 */}
                <div style={{
                  position: 'absolute',
                  top: '10px',
                  left: '10px',
                  background: 'rgba(139, 92, 246, 0.9)',
                  color: '#fff',
                  padding: '4px 8px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  fontWeight: '600',
                  backdropFilter: 'blur(4px)',
                  boxShadow: '0 2px 8px rgba(139, 92, 246, 0.3)',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}>
                  <CrownOutlined style={{ fontSize: '12px', color: '#fff' }} />
                  精选
                </div>

                {/* 类型标签 - 右上角 */}
                <div style={{
                  position: 'absolute',
                  top: '10px',
                  right: '10px',
                  background: '#6B7280',
                  color: '#fff',
                  padding: '4px 8px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  fontWeight: '500'
                }}>
                  {work.type}
                </div>
              </div>

              {/* 内容区域 - 增加高度 */}
              <div
                className="featured-content-area"
                style={{
                  padding: '16px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between'
                }}
              >
                {/* 上部水平布局区域：用户信息（左侧）+ 作品标题（右侧） */}
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginBottom: '12px'
                }}>
                  {/* 左侧：作者信息区域 */}
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      padding: '2px',
                      borderRadius: '6px',
                      flex: '0 0 auto'
                    }}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleCreatorClick(work.author.id)
                    }}
                    onMouseEnter={(e) => {
                      const avatar = e.currentTarget.querySelector('.creator-avatar') as HTMLElement
                      if (avatar) {
                        avatar.style.transform = 'scale(1.05) rotate(2deg)'
                      }
                    }}
                    onMouseLeave={(e) => {
                      const avatar = e.currentTarget.querySelector('.creator-avatar') as HTMLElement
                      if (avatar) {
                        avatar.style.transform = 'scale(1) rotate(0deg)'
                      }
                    }}
                    onMouseDown={(e) => {
                      const avatar = e.currentTarget.querySelector('.creator-avatar') as HTMLElement
                      if (avatar) {
                        avatar.style.transform = 'scale(0.95) rotate(0deg)'
                        setTimeout(() => {
                          avatar.style.transform = 'scale(1) rotate(0deg)'
                        }, 150)
                      }
                    }}
                    aria-label={`查看创作者 ${work.author.name} 的主页`}
                    title={`点击查看 ${work.author.name} 的主页`}
                  >
                    <Avatar
                      src={work.author.avatar}
                      size={32}
                      className="creator-avatar"
                      style={{
                        marginRight: '8px',
                        transition: 'transform 0.3s ease'
                      }}
                    />
                    <div>
                      {/* 第一行：创作者姓名 */}
                      <div style={{
                        fontSize: '12px',
                        fontWeight: 'bold',
                        color: '#fff',
                        lineHeight: '1.2',
                        marginBottom: '2px'
                      }}>
                        {work.author.name}
                      </div>
                      {/* 第二行：身份标签 */}
                      <div style={{
                        fontSize: '10px',
                        color: '#999'
                      }}>
                        {work.author.type}
                      </div>
                    </div>
                  </div>

                  {/* 右侧：作品标题 - 右对齐 */}
                  <div style={{
                    textAlign: 'right',
                    flex: '1 1 auto',
                    marginLeft: '12px'
                  }}>
                    <div style={{
                      fontSize: '13px',
                      fontWeight: 'bold',
                      color: '#fff',
                      lineHeight: '1.2'
                    }}>
                      {work.title}
                    </div>
                  </div>
                </div>

                {/* 统计信息 */}
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-around',
                  fontSize: '12px',
                  color: '#888',
                  borderTop: '1px solid #3a3a3a',
                  paddingTop: '8px'
                }}>
                  <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                    <EyeOutlined /> {work.views}
                  </span>
                  <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                    <DownloadOutlined /> {work.downloads}
                  </span>
                  <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                    <HeartOutlined /> {work.likes}
                  </span>
                </div>
              </div>
            </div>
          </Col>
        ))}
      </Row>
    </div>
  )
}
