import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Row, Col, Carousel, Card, Avatar, Typography, Badge, Tooltip } from 'antd'
import {
  CrownOutlined,
  DownloadOutlined,
  HeartOutlined,
  FileTextOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'

const { Title, Text } = Typography

// 轮播图数据接口
interface BannerItem {
  id: number
  title: string
  subtitle: string
  description?: string
  image: string
  link: string
  priority: number
  status: 'active' | 'inactive' | 'scheduled'
  publishTime?: Date
  endTime?: Date
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

// 创作者数据接口
interface Creator {
  id: number
  rank: number
  username: string
  avatar: string
  downloads: number
  works: number
  likes: number
  badge: string
  trend: 'up' | 'down' | 'stable'
  score: number
  userType: '个人创作者' | '企业创作者'
  lastWeekRank?: number
}

export const HeroBanner: React.FC = () => {
  const navigate = useNavigate()
  const [bannerItems, setBannerItems] = useState<BannerItem[]>([])
  const [topCreators, setTopCreators] = useState<Creator[]>([])
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())
  const [loading, setLoading] = useState(false)

  // 计算创作者排名分数的算法
  // 紫色数字含义：综合评分系统，基于加权算法计算用户综合实力
  // 计算公式：(下载量权重40% + 作品数权重30% + 点赞数权重30%) × 100
  // 分数范围：0-100分，分数越高排名越靠前
  const calculateCreatorScore = (creator: Omit<Creator, 'score' | 'rank'>) => {
    const downloadWeight = 0.4  // 下载量权重：40%
    const worksWeight = 0.3     // 作品数权重：30%
    const likesWeight = 0.3     // 点赞数权重：30%

    // 标准化分数（基于最大值进行归一化处理）
    const maxDownloads = 20000  // 下载量基准值
    const maxWorks = 100        // 作品数基准值
    const maxLikes = 5000       // 点赞数基准值

    const downloadScore = (creator.downloads / maxDownloads) * downloadWeight
    const worksScore = (creator.works / maxWorks) * worksWeight
    const likesScore = (creator.likes / maxLikes) * likesWeight

    // 返回0-100范围内的综合评分
    return (downloadScore + worksScore + likesScore) * 100
  }

  // 模拟从管理员后台获取轮播图数据
  const fetchBannerItems = async () => {
    // 模拟API调用
    const mockBannerData: BannerItem[] = [
      {
        id: 1,
        title: 'AI绘画大赛开始啦！',
        subtitle: '参与即有机会获得丰厚奖品',
        description: '全球AI创作者齐聚，展示最新AI绘画技术',
        image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=300&fit=crop',
        link: '/contest',
        priority: 1,
        status: 'active',
        publishTime: new Date('2025-01-01'),
        endTime: new Date('2025-02-01'),
        createdBy: 'admin',
        createdAt: new Date('2024-12-15'),
        updatedAt: new Date('2025-01-09')
      },
      {
        id: 2,
        title: '新功能上线：LoRA训练',
        subtitle: '一键训练专属AI模型',
        description: '革命性的LoRA训练技术，让AI模型训练更简单',
        image: 'https://images.unsplash.com/photo-1676299081847-824916de030a?w=800&h=300&fit=crop',
        link: '/lora-training',
        priority: 2,
        status: 'active',
        publishTime: new Date('2025-01-05'),
        createdBy: 'admin',
        createdAt: new Date('2025-01-01'),
        updatedAt: new Date('2025-01-09')
      },
      {
        id: 3,
        title: '创作者激励计划',
        subtitle: '优质作品获得平台推广',
        description: '加入我们的创作者激励计划，获得更多曝光机会',
        image: 'https://images.unsplash.com/photo-1686191128892-c0c8b8e5e3b8?w=800&h=300&fit=crop',
        link: '/creator-program',
        priority: 3,
        status: 'active',
        publishTime: new Date('2025-01-08'),
        createdBy: 'admin',
        createdAt: new Date('2025-01-05'),
        updatedAt: new Date('2025-01-09')
      }
    ]

    // 过滤活跃状态的轮播图并按优先级排序
    const activeBanners = mockBannerData
      .filter(item => item.status === 'active')
      .sort((a, b) => a.priority - b.priority)

    setBannerItems(activeBanners)
  }

  // 模拟获取创作者数据并计算排名
  const fetchTopCreators = async () => {
    setLoading(true)

    // 模拟API调用
    const mockCreatorData = [
      {
        id: 1,
        username: 'AI大师',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        downloads: 15420,
        works: 89,
        likes: 3240,
        lastWeekRank: 1,
        userType: '个人创作者' as const
      },
      {
        id: 2,
        username: '创意工坊',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
        downloads: 12350,
        works: 67,
        likes: 2890,
        lastWeekRank: 3,
        userType: '企业创作者' as const
      },
      {
        id: 3,
        username: '模型专家',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
        downloads: 9870,
        works: 45,
        likes: 2156,
        lastWeekRank: 2,
        userType: '个人创作者' as const
      },
      {
        id: 4,
        username: '数字艺术家',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
        downloads: 8950,
        works: 52,
        likes: 1980,
        lastWeekRank: 4,
        userType: '个人创作者' as const
      }
    ]

    // 计算分数并排序
    const creatorsWithScores = mockCreatorData.map(creator => ({
      ...creator,
      score: calculateCreatorScore(creator),
      rank: 0,
      badge: '',
      trend: 'stable' as const
    }))

    // 按分数排序并分配排名
    creatorsWithScores.sort((a, b) => b.score - a.score)

    const rankedCreators: Creator[] = creatorsWithScores.slice(0, 3).map((creator, index) => {
      const rank = index + 1
      const badges = ['🥇', '🥈', '🥉']

      // 计算趋势
      let trend: 'up' | 'down' | 'stable' = 'stable'
      if (creator.lastWeekRank) {
        if (rank < creator.lastWeekRank) trend = 'up'
        else if (rank > creator.lastWeekRank) trend = 'down'
      }

      return {
        ...creator,
        rank,
        badge: badges[index],
        trend
      }
    })

    setTopCreators(rankedCreators)
    setLastUpdated(new Date())
    setLoading(false)
  }

  // 组件挂载时获取数据
  useEffect(() => {
    fetchBannerItems()
    fetchTopCreators()

    // 设置定时更新（每小时更新一次英雄榜）
    const interval = setInterval(() => {
      fetchTopCreators()
    }, 60 * 60 * 1000) // 1小时

    return () => clearInterval(interval)
  }, [])

  // 手动刷新英雄榜
  const handleRefreshLeaderboard = () => {
    fetchTopCreators()
  }

  // 用户卡片点击跳转处理
  const handleUserCardClick = () => {
    navigate('/users/ranking')
  }

  return (
    <div style={{ height: '350px', padding: '0 24px', backgroundColor: '#1a1a1a' }}>
      <Row gutter={16} style={{ height: '100%' }}>
        {/* 轮播图区域 - 占4/5 */}
        <Col span={19} style={{ height: '100%' }}>
          <Carousel
            autoplay
            dots={true}
            style={{ height: '100%', borderRadius: '8px', overflow: 'hidden' }}
          >
            {bannerItems.map(item => (
              <div key={item.id}>
                <div
                  className="hero-banner-slide"
                  style={{
                    height: '350px',
                    backgroundImage: `linear-gradient(135deg, rgba(139, 92, 246, 0.8), rgba(168, 85, 247, 0.8)), url(${item.image})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#fff',
                    textAlign: 'center',
                    cursor: 'pointer',
                    borderRadius: '12px',
                    boxShadow: '0 8px 32px rgba(139, 92, 246, 0.3)',
                    transition: 'all 0.3s ease',
                    position: 'relative',
                    overflow: 'hidden',
                    border: '1px solid transparent'
                  }}
                  onClick={() => window.location.href = item.link}
                >
                  {/* 主要内容 */}
                  <div style={{ zIndex: 1, position: 'relative' }}>
                    <Title level={2} style={{ color: '#fff', marginBottom: '8px' }}>
                      {item.title}
                    </Title>
                    <Text style={{ color: '#fff', fontSize: '16px', display: 'block', marginBottom: '8px' }}>
                      {item.subtitle}
                    </Text>
                    {item.description && (
                      <Text style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '14px' }}>
                        {item.description}
                      </Text>
                    )}
                  </div>

                  {/* 发布信息标签 */}
                  <div style={{
                    position: 'absolute',
                    top: '12px',
                    right: '12px',
                    background: 'rgba(0, 0, 0, 0.5)',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    color: '#fff'
                  }}>
                    {item.publishTime && `发布于 ${item.publishTime.toLocaleDateString()}`}
                  </div>
                </div>
              </div>
            ))}
          </Carousel>
        </Col>

        {/* 英雄榜区域 - 占1/5 */}
        <Col span={5} style={{ height: '100%' }}>
          <Card
            className="hero-leaderboard-card"
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '8px' }}>
                <div>
                  <CrownOutlined style={{ color: '#8B5CF6', marginRight: '8px' }} />
                  <span style={{ color: '#fff' }}>英雄榜</span>
                </div>
                <Tooltip title="点击刷新排名">
                  <ClockCircleOutlined
                    style={{
                      color: '#888',
                      cursor: 'pointer',
                      fontSize: '12px'
                    }}
                    onClick={handleRefreshLeaderboard}
                  />
                </Tooltip>
              </div>
            }
            style={{
              height: '350px',
              backgroundColor: '#2a2a2a',
              borderColor: '#3a3a3a',
              borderRadius: '12px',
              overflow: 'hidden'
            }}
            headStyle={{
              backgroundColor: '#2a2a2a',
              borderBottom: '1px solid #3a3a3a',
              minHeight: '40px',
              height: '40px'
            }}
            bodyStyle={{
              padding: '8px',
              backgroundColor: '#2a2a2a',
              height: '310px', // 350px - 40px header高度
              overflow: 'hidden'
            }}
          >
            {/* 最后更新时间 */}
            <div style={{
              fontSize: '9px',
              color: '#666',
              textAlign: 'center',
              marginBottom: '8px',
              borderBottom: '1px solid #3a3a3a',
              paddingBottom: '6px'
            }}>
              更新于 {lastUpdated.toLocaleTimeString()}
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '6px' }}>
              {loading ? (
                <div style={{ textAlign: 'center', color: '#888', padding: '15px' }}>
                  计算排名中...
                </div>
              ) : (
                topCreators.map(creator => (
                  <div
                    key={creator.id}
                    className="hero-creator-card"
                    onClick={handleUserCardClick}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '8px',
                      backgroundColor: creator.rank === 1 ? 'rgba(139, 92, 246, 0.1)' : '#3a3a3a',
                      borderRadius: '8px',
                      border: creator.rank === 1 ? '1px solid #8B5CF6' : '1px solid transparent',
                      transition: 'all 0.3s ease',
                      cursor: 'pointer',
                      minHeight: '57px'
                    }}
                  >
                    {/* 排名徽章和趋势 */}
                    <div style={{
                      marginRight: '8px',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      minWidth: '24px',
                      height: '45px',
                      flexShrink: 0
                    }}>
                      <div style={{ fontSize: '30px', lineHeight: '1' }}>
                        {creator.badge}
                      </div>
                      {/* 趋势指示器 */}
                      <div style={{ fontSize: '8px', marginTop: '1px' }}>
                        {creator.trend === 'up' && (
                          <ArrowUpOutlined style={{ color: '#10B981' }} />
                        )}
                        {creator.trend === 'down' && (
                          <ArrowDownOutlined style={{ color: '#EF4444' }} />
                        )}
                        {creator.trend === 'stable' && (
                          <MinusOutlined style={{ color: '#888' }} />
                        )}
                      </div>
                    </div>

                    <Avatar
                      src={creator.avatar}
                      size={45}
                      style={{
                        marginRight: '8px',
                        flexShrink: 0,
                        backgroundColor: '#4a4a4a',
                        border: '1px solid #666'
                      }}
                    />

                    <div style={{
                      flex: 1,
                      minWidth: 0,
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center'
                    }}>
                      {/* 用户名和分数 */}
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginBottom: '2px'
                      }}>
                        <div style={{ flex: 1 }}>
                          <div style={{
                            fontSize: '11px',
                            fontWeight: 'bold',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            color: '#fff',
                            marginBottom: '1px'
                          }}>
                            {creator.username}
                          </div>
                          <div style={{
                            fontSize: '8px',
                            color: '#888',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}>
                            {creator.userType}
                          </div>
                        </div>
                        {/* 综合评分 - 紫色数字含义：基于下载量40%+作品数30%+点赞数30%的加权算法计算的综合实力评分(0-100分) */}
                        <Tooltip title={`综合评分: ${creator.score.toFixed(1)}分\n计算方式: 下载量(40%) + 作品数(30%) + 点赞数(30%)\n分数越高排名越靠前`}>
                          <div style={{
                            fontSize: '9px',
                            color: '#8B5CF6',
                            fontWeight: 'bold',
                            marginLeft: '4px',
                            cursor: 'help'
                          }}>
                            {creator.score.toFixed(0)}
                          </div>
                        </Tooltip>
                      </div>

                      {/* 统计数据 - 右对齐 */}
                      <div style={{
                        fontSize: '9px',
                        color: '#888',
                        display: 'flex',
                        justifyContent: 'flex-end',
                        gap: '6px',
                        marginTop: '4px'
                      }}>
                        <Tooltip title={`下载量: ${creator.downloads.toLocaleString()}`}>
                          <span>📥{(creator.downloads / 1000).toFixed(1)}k</span>
                        </Tooltip>
                        <Tooltip title={`作品数: ${creator.works}`}>
                          <span>📄{creator.works}</span>
                        </Tooltip>
                        <Tooltip title={`点赞数: ${creator.likes.toLocaleString()}`}>
                          <span>❤️{(creator.likes / 1000).toFixed(1)}k</span>
                        </Tooltip>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* 排名算法说明 */}
            <div style={{
              fontSize: '7px',
              color: '#666',
              textAlign: 'center',
              marginTop: '28px',
              paddingTop: '6px',
              borderTop: '1px solid #3a3a3a'
            }}>
              排名基于系统自动统计全站用户数据结果
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
}
