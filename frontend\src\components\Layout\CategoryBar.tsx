import React from 'react'
import { Tag, Space, Tooltip } from 'antd'
import { Link } from 'react-router-dom'

export const CategoryBar: React.FC = () => {
  const categories = [
    { key: 'video', label: '视频', color: '#8B5CF6', description: '视频生成、编辑和处理相关的AI资源' },
    { key: 'audio', label: '音频', color: '#06B6D4', description: '音频生成、语音合成和音乐创作工具' },
    { key: 'image', label: '图片', color: '#10B981', description: '图像生成、编辑和风格转换模型' },
    { key: 'text', label: '文本', color: '#3B82F6', description: '文本生成、翻译和自然语言处理' },
    { key: '3d', label: '3D模型', color: '#A855F7', description: '3D建模、渲染和虚拟现实内容' },
    { key: 'animation', label: '动画', color: '#EC4899', description: '动画制作、角色动作和特效生成' },
    { key: 'code', label: '代码', color: '#22C55E', description: '代码生成、调试和程序开发辅助' },
    { key: 'data', label: '数据', color: '#F59E0B', description: '数据分析、可视化和机器学习模型' }
  ]

  return (
    <div style={{
      height: '46px',
      backgroundColor: '#262626',
      borderBottom: '1px solid #3a3a3a',
      display: 'flex',
      alignItems: 'center',
      padding: '0 24px',
      overflow: 'hidden'
    }}>
      <Space size="small" wrap>
        <span style={{ color: '#888', marginRight: '8px', fontSize: '14px' }}>分类:</span>
        {categories.map(category => (
          <Tooltip key={category.key} title={category.description} placement="bottom">
            <Link to={`/resources?category=${category.key}`}>
              <Tag
                color={category.color}
                style={{
                  cursor: 'pointer',
                  border: 'none',
                  fontSize: '12px',
                  padding: '4px 12px',
                  borderRadius: '6px',
                  fontWeight: '500',
                  transition: 'all 0.2s ease'
                }}
                className="category-tag"
              >
                {category.label}
              </Tag>
            </Link>
          </Tooltip>
        ))}
      </Space>
    </div>
  )
}
