import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import request from 'supertest'
import express from 'express'
import { authController } from '../../src/controllers/authController'
import { UserModel } from '../../src/models/User'
import { hashPassword, comparePassword } from '../../src/utils/password'
import { generateToken } from '../../src/utils/jwt'

// Mock dependencies
vi.mock('../../src/models/User')
vi.mock('../../src/utils/password')
vi.mock('../../src/utils/jwt')

const app = express()
app.use(express.json())

// Setup routes
app.post('/register', authController.register)
app.post('/login', authController.login)
app.post('/logout', authController.logout)

describe('AuthController', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('POST /register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        fullName: 'Test User'
      }

      const hashedPassword = 'hashedpassword123'
      const mockUser = {
        id: '1',
        ...userData,
        password: hashedPassword,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      vi.mocked(UserModel.findByEmail).mockResolvedValue(null)
      vi.mocked(UserModel.findByUsername).mockResolvedValue(null)
      vi.mocked(hashPassword).mockResolvedValue(hashedPassword)
      vi.mocked(UserModel.create).mockResolvedValue(mockUser)

      const response = await request(app)
        .post('/register')
        .send(userData)

      expect(response.status).toBe(201)
      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('User registered successfully')
      expect(response.body.data.user.email).toBe(userData.email)
      expect(response.body.data.user.password).toBeUndefined()
    })

    it('should return error if email already exists', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        fullName: 'Test User'
      }

      const existingUser = { id: '1', email: userData.email }
      vi.mocked(UserModel.findByEmail).mockResolvedValue(existingUser as any)

      const response = await request(app)
        .post('/register')
        .send(userData)

      expect(response.status).toBe(400)
      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('EMAIL_EXISTS')
    })

    it('should return error if username already exists', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        fullName: 'Test User'
      }

      const existingUser = { id: '1', username: userData.username }
      vi.mocked(UserModel.findByEmail).mockResolvedValue(null)
      vi.mocked(UserModel.findByUsername).mockResolvedValue(existingUser as any)

      const response = await request(app)
        .post('/register')
        .send(userData)

      expect(response.status).toBe(400)
      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('USERNAME_EXISTS')
    })

    it('should return validation error for invalid data', async () => {
      const invalidData = {
        username: 'a', // too short
        email: 'invalid-email',
        password: '123' // too short
      }

      const response = await request(app)
        .post('/register')
        .send(invalidData)

      expect(response.status).toBe(400)
      expect(response.body.success).toBe(false)
    })
  })

  describe('POST /login', () => {
    it('should login user successfully with email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      }

      const mockUser = {
        id: '1',
        email: loginData.email,
        password: 'hashedpassword',
        username: 'testuser',
        fullName: 'Test User'
      }

      const token = 'jwt-token'

      vi.mocked(UserModel.findByEmail).mockResolvedValue(mockUser as any)
      vi.mocked(comparePassword).mockResolvedValue(true)
      vi.mocked(generateToken).mockReturnValue(token)

      const response = await request(app)
        .post('/login')
        .send(loginData)

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.token).toBe(token)
      expect(response.body.data.user.email).toBe(loginData.email)
      expect(response.body.data.user.password).toBeUndefined()
    })

    it('should login user successfully with username', async () => {
      const loginData = {
        username: 'testuser',
        password: 'password123'
      }

      const mockUser = {
        id: '1',
        username: loginData.username,
        password: 'hashedpassword',
        email: '<EMAIL>',
        fullName: 'Test User'
      }

      const token = 'jwt-token'

      vi.mocked(UserModel.findByUsername).mockResolvedValue(mockUser as any)
      vi.mocked(comparePassword).mockResolvedValue(true)
      vi.mocked(generateToken).mockReturnValue(token)

      const response = await request(app)
        .post('/login')
        .send(loginData)

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.token).toBe(token)
    })

    it('should return error for invalid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      }

      const mockUser = {
        id: '1',
        email: loginData.email,
        password: 'hashedpassword'
      }

      vi.mocked(UserModel.findByEmail).mockResolvedValue(mockUser as any)
      vi.mocked(comparePassword).mockResolvedValue(false)

      const response = await request(app)
        .post('/login')
        .send(loginData)

      expect(response.status).toBe(401)
      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('INVALID_CREDENTIALS')
    })

    it('should return error for non-existent user', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      }

      vi.mocked(UserModel.findByEmail).mockResolvedValue(null)

      const response = await request(app)
        .post('/login')
        .send(loginData)

      expect(response.status).toBe(401)
      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('INVALID_CREDENTIALS')
    })
  })

  describe('POST /logout', () => {
    it('should logout user successfully', async () => {
      const response = await request(app)
        .post('/logout')

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Logged out successfully')
    })
  })
})
