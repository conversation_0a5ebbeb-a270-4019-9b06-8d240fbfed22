import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>u, Button, Space, Avatar, Typography } from 'antd'
import { 
  MenuOutlined,
  HomeOutlined,
  AppstoreOutlined,
  DashboardOutlined,
  UserOutlined,
  LoginOutlined,
  UserAddOutlined,
  LogoutOutlined
} from '@ant-design/icons'
import { Link, useNavigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'

const { Text } = Typography

export const MobileMenu: React.FC = () => {
  const [visible, setVisible] = useState(false)
  const { isAuthenticated, user, logout } = useAuth()
  const navigate = useNavigate()

  const handleLogout = () => {
    logout()
    setVisible(false)
    navigate('/')
  }

  const menuItems = [
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: <Link to="/" onClick={() => setVisible(false)}>首页</Link>
    },
    {
      key: 'resources',
      icon: <AppstoreOutlined />,
      label: <Link to="/resources" onClick={() => setVisible(false)}>资源市场</Link>
    },
    {
      key: 'categories',
      label: <Link to="/categories" onClick={() => setVisible(false)}>分类</Link>
    }
  ]

  if (isAuthenticated) {
    menuItems.push(
      {
        key: 'dashboard',
        icon: <DashboardOutlined />,
        label: <Link to="/dashboard" onClick={() => setVisible(false)}>控制台</Link>
      },
      {
        key: 'profile',
        icon: <UserOutlined />,
        label: <Link to="/profile" onClick={() => setVisible(false)}>个人资料</Link>
      }
    )
  }

  return (
    <>
      <Button
        type="text"
        icon={<MenuOutlined />}
        onClick={() => setVisible(true)}
        className="md:hidden"
      />
      
      <Drawer
        title={
          <Space>
            <Text strong>🤖 AIGC Service Hub</Text>
          </Space>
        }
        placement="left"
        onClose={() => setVisible(false)}
        open={visible}
        width={280}
      >
        {isAuthenticated && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <Space>
              <Avatar 
                src={user?.avatarUrl} 
                icon={<UserOutlined />}
                size="large"
              />
              <div>
                <Text strong className="block">{user?.fullName}</Text>
                <Text type="secondary" className="text-sm">@{user?.username}</Text>
              </div>
            </Space>
          </div>
        )}

        <Menu
          mode="vertical"
          items={menuItems}
          className="border-none"
        />

        <div className="absolute bottom-6 left-6 right-6">
          {isAuthenticated ? (
            <Button
              block
              icon={<LogoutOutlined />}
              onClick={handleLogout}
              danger
            >
              退出登录
            </Button>
          ) : (
            <Space direction="vertical" className="w-full">
              <Button
                block
                icon={<LoginOutlined />}
                onClick={() => {
                  navigate('/login')
                  setVisible(false)
                }}
              >
                登录
              </Button>
              <Button
                block
                type="primary"
                icon={<UserAddOutlined />}
                onClick={() => {
                  navigate('/register')
                  setVisible(false)
                }}
              >
                注册
              </Button>
            </Space>
          )}
        </div>
      </Drawer>
    </>
  )
}
