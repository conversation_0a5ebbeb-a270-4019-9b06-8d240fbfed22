import { Router } from 'express';
import { body, query } from 'express-validator';
import { authMiddleware } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';
import { reviewController } from '../controllers/reviewController';

const router = Router();

// Get reviews for a resource
router.get(
  '/resources/:resourceId',
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 })
  ],
  validateRequest,
  reviewController.getResourceReviews
);

// Create review for a resource
router.post(
  '/resources/:resourceId',
  authMiddleware,
  [
    body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
    body('comment').optional().isLength({ max: 1000 }).trim(),
    body('transactionId').optional().isUUID()
  ],
  validateRequest,
  reviewController.createReview
);

// Get user's reviews
router.get(
  '/my',
  authMiddleware,
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 })
  ],
  validateRequest,
  reviewController.getUserReviews
);

export default router;
