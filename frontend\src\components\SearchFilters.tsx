import React, { useState } from 'react'
import {
  Input,
  Select,
  <PERSON>lider,
  Card,
  Space,
  Button,
  Collapse,
  Tag,
  Typography,
  Row,
  Col,
  Divider
} from 'antd'
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
  SortAscendingOutlined
} from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import { SearchParams, Category } from '@/types'

const { Option } = Select
const { Panel } = Collapse
const { Text } = Typography

interface SearchFiltersProps {
  filters: SearchParams
  categories: Category[]
  onFiltersChange: (filters: SearchParams) => void
  onSearch: () => void
  loading?: boolean
}

export const SearchFilters: React.FC<SearchFiltersProps> = ({
  filters,
  categories,
  onFiltersChange,
  onSearch,
  loading = false
}) => {
  const { t } = useTranslation()
  const [priceRange, setPriceRange] = useState<[number, number]>([
    filters.minPrice || 0,
    filters.maxPrice || 1000
  ])

  const handleFilterChange = (key: keyof SearchParams, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const handlePriceRangeChange = (value: [number, number]) => {
    setPriceRange(value)
    onFiltersChange({
      ...filters,
      minPrice: value[0],
      maxPrice: value[1]
    })
  }

  const handleClearFilters = () => {
    setPriceRange([0, 1000])
    onFiltersChange({
      q: '',
      page: 1,
      limit: 20
    })
  }

  const resourceTypes = [
    { value: 'fine_tuned_model', label: t('resourceTypes.fine_tuned_model'), icon: '🤖' },
    { value: 'lora', label: t('resourceTypes.lora'), icon: '⚡' },
    { value: 'workflow', label: t('resourceTypes.workflow'), icon: '🔄' },
    { value: 'prompt', label: t('resourceTypes.prompt'), icon: '💬' },
    { value: 'tool', label: t('resourceTypes.tool'), icon: '🛠️' }
  ]

  const sortOptions = [
    { value: 'created_at', label: t('sortOptions.created_at') },
    { value: 'updated_at', label: t('sortOptions.updated_at') },
    { value: 'price', label: t('sortOptions.price') },
    { value: 'rating_average', label: t('sortOptions.rating_average') },
    { value: 'download_count', label: t('sortOptions.download_count') },
    { value: 'title', label: t('sortOptions.title') }
  ]

  const popularTags = [
    'AI', 'Machine Learning', 'NLP', 'Computer Vision', 'GPT', 'BERT',
    'Stable Diffusion', 'ChatGPT', 'LLM', 'Deep Learning', 'PyTorch', 'TensorFlow'
  ]

  return (
    <Card className="mb-6">
      <Space direction="vertical" size="large" className="w-full">
        {/* Search Bar */}
        <Row gutter={[16, 16]} align="middle">
          <Col flex="auto">
            <Input.Search
              placeholder={t('resources.searchPlaceholder')}
              value={filters.q}
              onChange={(e) => handleFilterChange('q', e.target.value)}
              onSearch={onSearch}
              enterButton={<SearchOutlined />}
              size="large"
              loading={loading}
            />
          </Col>
          <Col>
            <Button
              icon={<ClearOutlined />}
              onClick={handleClearFilters}
              disabled={loading}
            >
              清除
            </Button>
          </Col>
        </Row>

        {/* Quick Filters */}
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Text strong>分类</Text>
            <Select
              placeholder="所有分类"
              value={filters.category}
              onChange={(value) => handleFilterChange('category', value)}
              className="w-full mt-1"
              allowClear
              size="large"
            >
              {categories.map(category => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Text strong>资源类型</Text>
            <Select
              placeholder="所有类型"
              value={filters.type}
              onChange={(value) => handleFilterChange('type', value)}
              className="w-full mt-1"
              allowClear
              size="large"
            >
              {resourceTypes.map(type => (
                <Option key={type.value} value={type.value}>
                  <Space>
                    <span>{type.icon}</span>
                    <span className="hidden sm:inline">{type.label}</span>
                  </Space>
                </Option>
              ))}
            </Select>
          </Col>

          <Col xs={12} sm={6} lg={3}>
            <Text strong>排序方式</Text>
            <Select
              value={filters.sortBy || 'created_at'}
              onChange={(value) => handleFilterChange('sortBy', value)}
              className="w-full mt-1"
              size="large"
            >
              {sortOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Col>

          <Col xs={12} sm={6} lg={3}>
            <Text strong>排序</Text>
            <Select
              value={filters.sortOrder || 'DESC'}
              onChange={(value) => handleFilterChange('sortOrder', value)}
              className="w-full mt-1"
              size="large"
            >
              <Option value="DESC">
                <Space>
                  <SortAscendingOutlined />
                  <span className="hidden sm:inline">降序</span>
                </Space>
              </Option>
              <Option value="ASC">
                <Space>
                  <SortAscendingOutlined style={{ transform: 'rotate(180deg)' }} />
                  <span className="hidden sm:inline">升序</span>
                </Space>
              </Option>
            </Select>
          </Col>
        </Row>

        {/* Advanced Filters */}
        <Collapse ghost>
          <Panel 
            header={
              <Space>
                <FilterOutlined />
                <Text strong>高级筛选</Text>
              </Space>
            } 
            key="advanced"
          >
            <Space direction="vertical" size="large" className="w-full">
              {/* Price Range */}
              <div>
                <Text strong>价格区间: ${priceRange[0]} - ${priceRange[1]}</Text>
                <Slider
                  range
                  min={0}
                  max={1000}
                  value={priceRange}
                  onChange={handlePriceRangeChange}
                  className="mt-2"
                  marks={{
                    0: '$0',
                    250: '$250',
                    500: '$500',
                    750: '$750',
                    1000: '$1000+'
                  }}
                />
              </div>

              <Divider />

              {/* Popular Tags */}
              <div>
                <Text strong className="block mb-2">热门标签</Text>
                <Space wrap>
                  {popularTags.map(tag => (
                    <Tag.CheckableTag
                      key={tag}
                      checked={filters.tags?.includes(tag) || false}
                      onChange={(checked) => {
                        const currentTags = filters.tags || []
                        const newTags = checked
                          ? [...currentTags, tag]
                          : currentTags.filter(t => t !== tag)
                        handleFilterChange('tags', newTags)
                      }}
                    >
                      {tag}
                    </Tag.CheckableTag>
                  ))}
                </Space>
              </div>
            </Space>
          </Panel>
        </Collapse>

        {/* Active Filters Display */}
        {(filters.category || filters.type || filters.tags?.length || filters.minPrice || filters.maxPrice) && (
          <div>
            <Text strong className="block mb-2">当前筛选:</Text>
            <Space wrap>
              {filters.category && (
                <Tag closable onClose={() => handleFilterChange('category', undefined)}>
                  分类: {categories.find(c => c.id === filters.category)?.name}
                </Tag>
              )}
              {filters.type && (
                <Tag closable onClose={() => handleFilterChange('type', undefined)}>
                  类型: {resourceTypes.find(t => t.value === filters.type)?.label}
                </Tag>
              )}
              {filters.tags?.map(tag => (
                <Tag 
                  key={tag} 
                  closable 
                  onClose={() => {
                    const newTags = filters.tags?.filter(t => t !== tag)
                    handleFilterChange('tags', newTags)
                  }}
                >
                  {tag}
                </Tag>
              ))}
              {(filters.minPrice || filters.maxPrice) && (
                <Tag closable onClose={() => {
                  handleFilterChange('minPrice', undefined)
                  handleFilterChange('maxPrice', undefined)
                  setPriceRange([0, 1000])
                }}>
                  价格: ${filters.minPrice || 0} - ${filters.maxPrice || 1000}
                </Tag>
              )}
            </Space>
          </div>
        )}
      </Space>
    </Card>
  )
}
