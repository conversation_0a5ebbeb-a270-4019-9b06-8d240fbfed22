import jwt from 'jsonwebtoken';

export interface JwtPayload {
  id: string;
  email: string;
  username: string;
  userType: string;
  iat?: number;
  exp?: number;
}

export class JwtService {
  private static readonly secret = process.env.JWT_SECRET;
  private static readonly expiresIn = process.env.JWT_EXPIRES_IN || '7d';
  private static readonly refreshSecret = process.env.JWT_REFRESH_SECRET;
  private static readonly refreshExpiresIn = process.env.JWT_REFRESH_EXPIRES_IN || '30d';

  static generateToken(payload: Omit<JwtPayload, 'iat' | 'exp'>): string {
    if (!this.secret) {
      throw new Error('JWT_SECRET is not configured');
    }

    return jwt.sign(payload, this.secret, {
      expiresIn: this.expiresIn,
    });
  }

  static generateRefreshToken(payload: Omit<JwtPayload, 'iat' | 'exp'>): string {
    if (!this.refreshSecret) {
      throw new Error('JWT_REFRESH_SECRET is not configured');
    }

    return jwt.sign(payload, this.refreshSecret, {
      expiresIn: this.refreshExpiresIn,
    });
  }

  static verifyToken(token: string): JwtPayload {
    if (!this.secret) {
      throw new Error('JWT_SECRET is not configured');
    }

    try {
      return jwt.verify(token, this.secret) as JwtPayload;
    } catch (error) {
      throw new Error('Invalid or expired token');
    }
  }

  static verifyRefreshToken(token: string): JwtPayload {
    if (!this.refreshSecret) {
      throw new Error('JWT_REFRESH_SECRET is not configured');
    }

    try {
      return jwt.verify(token, this.refreshSecret) as JwtPayload;
    } catch (error) {
      throw new Error('Invalid or expired refresh token');
    }
  }

  static decodeToken(token: string): JwtPayload | null {
    try {
      return jwt.decode(token) as JwtPayload;
    } catch (error) {
      return null;
    }
  }
}
