version: '3.8'

services:
  # Development Database with additional tools
  postgres:
    environment:
      POSTGRES_DB: aigc_service_hub_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./database/seeds:/docker-entrypoint-initdb.d/seeds

  # Redis for development
  redis:
    volumes:
      - redis_dev_data:/data

  # Backend with development configuration
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    environment:
      NODE_ENV: development
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: aigc_service_hub_dev
      DB_USER: dev_user
      DB_PASSWORD: dev_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: dev-jwt-secret-key
      PAYPAL_CLIENT_ID: sandbox-paypal-client-id
      PAYPAL_CLIENT_SECRET: sandbox-paypal-client-secret
      PAYPAL_MODE: sandbox
      AWS_ACCESS_KEY_ID: dev-aws-access-key
      AWS_SECRET_ACCESS_KEY: dev-aws-secret-key
      AWS_REGION: us-west-2
      AWS_S3_BUCKET: aigc-service-hub-dev-files
      LOG_LEVEL: debug
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./logs:/app/logs
    command: npm run dev:watch

  # Frontend with development configuration
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    environment:
      NODE_ENV: development
      VITE_API_BASE_URL: http://localhost:3001/api
      VITE_PAYPAL_CLIENT_ID: sandbox-paypal-client-id
      VITE_APP_ENV: development
    volumes:
      - ./frontend:/app
      - /app/node_modules
    command: npm run dev

  # Database Admin Tool (pgAdmin)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: aigc-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - aigc-network
    depends_on:
      - postgres
    profiles:
      - tools

  # Redis Commander
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: aigc-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - aigc-network
    depends_on:
      - redis
    profiles:
      - tools

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  pgadmin_data:
    driver: local
