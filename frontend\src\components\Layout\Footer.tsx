import React from 'react'
import { Layout, Row, Col, Typography, Space, Divider } from 'antd'
import { 
  GithubOutlined, 
  TwitterOutlined, 
  LinkedinOutlined,
  MailOutlined 
} from '@ant-design/icons'

const { Footer: AntFooter } = Layout
const { Text, Link } = Typography

export const Footer: React.FC = () => {
  return (
    <AntFooter style={{
      backgroundColor: '#0f0f0f',
      color: '#fff',
      padding: '20px 0',
      height: '80px',
      display: 'flex',
      alignItems: 'center'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 24px',
        width: '100%'
      }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space size="large">
              <Text strong style={{ color: '#fff', fontSize: '16px' }}>
                🤖 AIGC Service Hub
              </Text>
              <Text style={{ color: '#888' }}>
                领先的AI资源、模型和工具市场平台
              </Text>
            </Space>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <div>
              <Text strong style={{ color: '#fff', display: 'block', marginBottom: '16px' }}>
                资源市场
              </Text>
              <Space direction="vertical" size="small">
                <Link href="/resources" style={{ color: '#888' }}>
                  浏览资源
                </Link>
                <Link href="/categories" style={{ color: '#888' }}>
                  分类
                </Link>
                <Link href="/creators" style={{ color: '#888' }}>
                  顶级创作者
                </Link>
                <Link href="/featured" style={{ color: '#888' }}>
                  精选资源
                </Link>
              </Space>
            </div>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <div>
              <Text strong className="text-white block mb-4">
                创作者中心
              </Text>
              <Space direction="vertical" size="small">
                <Link href="/dashboard" className="text-gray-300 hover:text-white">
                  创作者控制台
                </Link>
                <Link href="/upload" className="text-gray-300 hover:text-white">
                  上传资源
                </Link>
                <Link href="/earnings" className="text-gray-300 hover:text-white">
                  收益管理
                </Link>
                <Link href="/guidelines" className="text-gray-300 hover:text-white">
                  创作指南
                </Link>
              </Space>
            </div>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <div>
              <Text strong className="text-white block mb-4">
                帮助支持
              </Text>
              <Space direction="vertical" size="small">
                <Link href="/help" className="text-gray-300 hover:text-white">
                  帮助中心
                </Link>
                <Link href="/contact" className="text-gray-300 hover:text-white">
                  联系我们
                </Link>
                <Link href="/api" className="text-gray-300 hover:text-white">
                  API文档
                </Link>
                <Link href="/status" className="text-gray-300 hover:text-white">
                  系统状态
                </Link>
              </Space>
            </div>
          </Col>
        </Row>

        <Divider className="border-gray-700 my-8" />

        <Row justify="space-between" align="middle">
          <Col>
            <Space split={<span className="text-gray-500">•</span>}>
              <Link href="/privacy" className="text-gray-400 hover:text-white">
                隐私政策
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white">
                服务条款
              </Link>
              <Link href="/cookies" className="text-gray-400 hover:text-white">
                Cookie政策
              </Link>
            </Space>
          </Col>

          <Col>
            <Space size="large">
              <Link href="https://github.com" className="text-gray-400 hover:text-white">
                <GithubOutlined style={{ fontSize: '20px' }} />
              </Link>
              <Link href="https://twitter.com" className="text-gray-400 hover:text-white">
                <TwitterOutlined style={{ fontSize: '20px' }} />
              </Link>
              <Link href="https://linkedin.com" className="text-gray-400 hover:text-white">
                <LinkedinOutlined style={{ fontSize: '20px' }} />
              </Link>
              <Link href="mailto:<EMAIL>" className="text-gray-400 hover:text-white">
                <MailOutlined style={{ fontSize: '20px' }} />
              </Link>
            </Space>
          </Col>
        </Row>

        <div className="text-center mt-8">
          <Text className="text-gray-400">
            © 2025 AIGC Service Hub. 保留所有权利。为AI社区用❤️构建。
          </Text>
        </div>
      </div>
    </AntFooter>
  )
}
