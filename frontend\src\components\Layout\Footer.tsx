import React from 'react'
import { Layout, Row, Col, Typo<PERSON>, Space, Divider } from 'antd'
import { useNavigate } from 'react-router-dom'
import {
  GithubOutlined,
  TwitterOutlined,
  LinkedinOutlined,
  YoutubeOutlined,
  MessageOutlined
} from '@ant-design/icons'
import styles from './Footer.module.css'

const { Footer: AntFooter } = Layout
const { Text, Link } = Typography

export const Footer: React.FC = () => {
  const navigate = useNavigate()

  const handleNavigation = (path: string) => {
    navigate(path)
  }

  const socialLinks = [
    { icon: <YoutubeOutlined />, url: 'https://youtube.com/@aigc-service-hub', label: 'YouTube' },
    { icon: <TwitterOutlined />, url: 'https://twitter.com/aigc_service_hub', label: 'Twitter/X' },
    { icon: <LinkedinOutlined />, url: 'https://linkedin.com/company/aigc-service-hub', label: 'LinkedIn' },
    { icon: <GithubOutlined />, url: 'https://github.com/aigc-service-hub', label: 'GitHub' },
    { icon: <MessageOutlined />, url: 'https://discord.gg/aigc-service-hub', label: 'Discord' }
  ]

  const aboutLinks = [
    { label: '招贤纳士', path: '/careers' },
    { label: '资讯及活动中心', path: '/news' },
    { label: '技术支持与交流', path: '/support' },
    { label: '帮助中心', path: '/help' }
  ]

  const legalLinks = [
    { label: '与 AIGC Service Hub 联系', path: '/contact' },
    { label: '用户协议', path: '/terms' },
    { label: '隐私条款', path: '/privacy' },
    { label: '使用条款', path: '/usage' },
    { label: '商标', path: '/trademark' },
    { label: '关于我们的广告', path: '/ads' }
  ]

  return (
    <AntFooter className={styles.footer}>
      <div className={styles.footerContainer}>
        {/* Main Content Area */}
        <div className={styles.mainContent}>
          {/* Left Side - Logo and Description */}
          <div className={styles.leftSection}>
            {/* Logo */}
            <div className={styles.logoContainer}>
              <img
                src="/aigc-logo-optimized.svg"
                alt="AIGC Service Hub"
                className={styles.logo}
                onClick={() => handleNavigation('/')}
              />
            </div>

            {/* Platform Description */}
            <div className={styles.descriptionContainer}>
              <Text className={styles.description}>
                AIGC Service Hub 是一个致力于AI资源交易创收的创新平台，旨在为全球的个人创作者和企业创作者提供高效的资源共享与交易渠道。平台依托完善的运营管理机制，确保交易的高效性与公平性，助力创作者实现增收盈利，同时推动AI技术的创新与广泛应用。
              </Text>
            </div>
          </div>

          {/* Right Side - Social Media and Links */}
          <div className={styles.rightSection}>
            {/* Social Media Section - 右对齐，与Logo顶部对齐 */}
            <div className={styles.socialSection}>
              <div className={styles.socialContent}>
                <Text strong className={styles.sectionTitle}>
                  请关注 AIGC Service Hub
                </Text>
                <div className={styles.socialIcons}>
                  {socialLinks.map((social, index) => (
                    <a
                      key={index}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={styles.socialLink}
                      title={social.label}
                    >
                      {social.icon}
                    </a>
                  ))}
                </div>
              </div>
            </div>

            {/* About Links Section - 与社交媒体左边缘对齐，与介绍文字顶部对齐 */}
            <div className={styles.aboutSection}>
              <Text strong className={styles.sectionTitle}>
                关于 AIGC Service Hub
              </Text>
              <div className={styles.aboutLinks}>
                {aboutLinks.map((link, index) => (
                  <a
                    key={index}
                    onClick={() => handleNavigation(link.path)}
                    className={styles.aboutLink}
                  >
                    {link.label}
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Legal Information Section - 完全左对齐 */}
        <div className={styles.legalSection}>
          {/* Divider - 完全左对齐 */}
          <Divider className={styles.divider} />

          {/* Legal Links - 左对齐 */}
          <div className={styles.legalLinksContainer}>
            <Space
              split={<span style={{ color: '#666', margin: '0 8px' }}>|</span>}
              wrap
              size="small"
              className={styles.legalLinksSpace}
            >
              {legalLinks.map((link, index) => (
                <a
                  key={index}
                  onClick={() => handleNavigation(link.path)}
                  className={styles.legalLink}
                >
                  {link.label}
                </a>
              ))}
            </Space>
          </div>

          {/* Registration Information - 左对齐 */}
          <div className={styles.registrationContainer}>
            <Text className={styles.registrationInfo}>
              津ICP备2025029783号 | 网信算备110112129623601230018号 | 津公网安备 33011002011098号
            </Text>
            <Text className={styles.registrationInfo}>
              生成式人工智能服务管理暂行办法备案号 TianJin-Lora-20250228
            </Text>
          </div>

          {/* Copyright - 左对齐 */}
          <div className={styles.copyrightContainer}>
            <Text className={styles.copyright}>
              © AIGC Service Hub 2025
            </Text>
          </div>
        </div>
      </div>
    </AntFooter>
  )
}
