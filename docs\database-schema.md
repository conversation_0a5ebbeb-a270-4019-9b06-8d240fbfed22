# AIGC Service Hub - Database Schema

## Overview
This document describes the database schema for the AIGC Service Hub platform, designed to support AI resource trading, user management, and transaction processing.

## Core Tables

### Users Table
Primary user authentication and basic information.

```sql
users (
    id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    username VA<PERSON>HAR(100) UNIQUE NOT NULL,
    full_name VA<PERSON><PERSON><PERSON>(255),
    user_type VARCHAR(20) DEFAULT 'individual', -- 'individual' | 'enterprise'
    avatar_url TEXT,
    bio TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
```

### User Profiles Table
Extended user information and preferences.

```sql
user_profiles (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    company_name VARCHAR(255), -- For enterprise users
    website_url TEXT,
    social_links JSONB,
    skills TEXT[],
    experience_level VARCHAR(50), -- 'beginner' | 'intermediate' | 'advanced' | 'expert'
    preferred_categories UUID[],
    notification_settings JSONB,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
```

### Categories Table
Resource categorization system.

```sql
categories (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    icon_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE
)
```

### Resources Table
Core resource information for AI assets.

```sql
resources (
    id UUID PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    resource_type VARCHAR(50) NOT NULL, -- 'fine_tuned_model' | 'lora' | 'workflow' | 'prompt' | 'tool'
    category_id UUID REFERENCES categories(id),
    creator_id UUID REFERENCES users(id),
    price DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'USD',
    file_url TEXT,
    file_size BIGINT,
    file_type VARCHAR(100),
    thumbnail_url TEXT,
    tags TEXT[],
    download_count INTEGER DEFAULT 0,
    rating_average DECIMAL(3, 2) DEFAULT 0.00,
    rating_count INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending' | 'approved' | 'rejected'
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
```

### Resource Files Table
File management for resources with multiple files.

```sql
resource_files (
    id UUID PRIMARY KEY,
    resource_id UUID REFERENCES resources(id),
    file_name VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    mime_type VARCHAR(100),
    is_primary BOOLEAN DEFAULT FALSE,
    upload_status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE
)
```

### Transactions Table
Payment and transaction records.

```sql
transactions (
    id UUID PRIMARY KEY,
    buyer_id UUID REFERENCES users(id),
    seller_id UUID REFERENCES users(id),
    resource_id UUID REFERENCES resources(id),
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    platform_fee DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    seller_earnings DECIMAL(10, 2) NOT NULL,
    payment_method VARCHAR(50) DEFAULT 'paypal',
    payment_id TEXT, -- PayPal transaction ID
    status VARCHAR(20) DEFAULT 'pending', -- 'pending' | 'completed' | 'failed' | 'refunded'
    created_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE
)
```

### Reviews Table
User reviews and ratings for resources.

```sql
reviews (
    id UUID PRIMARY KEY,
    resource_id UUID REFERENCES resources(id),
    reviewer_id UUID REFERENCES users(id),
    transaction_id UUID REFERENCES transactions(id),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    is_verified_purchase BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE
)
```

## Supporting Tables

### Favorites Table
User favorite resources.

### Collections Table
User-created resource collections.

### Notifications Table
System notifications for users.

### Payment Methods Table
User payment method preferences.

### Earnings Table
Creator earnings tracking.

### Withdrawals Table
Creator withdrawal requests.

## Relationships

```
users (1) ←→ (1) user_profiles
users (1) ←→ (n) resources (as creator)
users (1) ←→ (n) transactions (as buyer/seller)
users (1) ←→ (n) reviews (as reviewer)
users (1) ←→ (n) favorites
users (1) ←→ (n) collections

categories (1) ←→ (n) resources
resources (1) ←→ (n) resource_files
resources (1) ←→ (n) resource_versions
resources (1) ←→ (n) transactions
resources (1) ←→ (n) reviews
resources (1) ←→ (n) favorites

transactions (1) ←→ (n) earnings
```

## Indexes

Performance indexes are created for:
- User lookups (email, username)
- Resource queries (creator, category, type, status)
- Transaction queries (buyer, seller, resource, status)
- Review queries (resource)
- Notification queries (user, unread status)

## Data Types

- **UUID**: Primary keys and foreign keys
- **VARCHAR**: Text fields with length limits
- **TEXT**: Unlimited text fields
- **JSONB**: Structured data (settings, metadata)
- **TEXT[]**: Array fields (tags, skills)
- **DECIMAL**: Monetary values
- **BOOLEAN**: True/false flags
- **TIMESTAMP WITH TIME ZONE**: Date/time fields
