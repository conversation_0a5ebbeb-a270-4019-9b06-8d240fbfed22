import React from 'react'
import { <PERSON> } from 'react-router-dom'
import {
  Layout,
  Row,
  Col,
  Button,
  Typography,
  Card,
  Space,
  Statistic,
  Progress,
  Tag
} from 'antd'
import {
  RocketOutlined,
  ThunderboltOutlined,
  TeamOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  SyncOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import { useAuth } from '@/hooks/useAuth'

const { Content } = Layout
const { Title, Paragraph, Text } = Typography

export const Home: React.FC = () => {
  const { isAuthenticated } = useAuth()
  const { t } = useTranslation()

  return (
    <Content>
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <Row justify="center" align="middle">
            <Col xs={24} lg={16} className="text-center">
              <Title level={1} className="!text-white !mb-6">
                <span className="text-3xl sm:text-4xl lg:text-6xl">
                  {t('home.title')}
                </span>
              </Title>
              <Paragraph className="!text-white !text-lg sm:!text-xl !mb-8 opacity-90">
                {t('home.subtitle')}
              </Paragraph>
              <Space size="large" wrap>
                {!isAuthenticated ? (
                  <>
                    <Button
                      type="primary"
                      size="large"
                      icon={<RocketOutlined />}
                      className="bg-white text-blue-600 border-white hover:bg-gray-100"
                    >
                      <Link to="/register">{t('home.getStarted')}</Link>
                    </Button>
                    <Button
                      size="large"
                      ghost
                      icon={<ThunderboltOutlined />}
                    >
                      <Link to="/resources">{t('home.browseResources')}</Link>
                    </Button>
                  </>
                ) : (
                  <Button
                    type="primary"
                    size="large"
                    icon={<RocketOutlined />}
                    className="bg-white text-blue-600 border-white hover:bg-gray-100"
                  >
                    <Link to="/dashboard">{t('home.goToDashboard')}</Link>
                  </Button>
                )}
              </Space>
            </Col>
          </Row>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <Row justify="center" className="mb-16">
          <Col xs={24} lg={16} className="text-center">
            <Title level={2}>AI开发所需的一切</Title>
            <Paragraph className="text-lg text-gray-600">
              访问由全球专家创建的综合AI资源市场
            </Paragraph>
          </Col>
        </Row>

        <Row gutter={[32, 32]}>
          <Col xs={24} sm={12} lg={8}>
            <Card className="text-center h-full" hoverable>
              <div className="text-4xl mb-4">🤖</div>
              <Title level={4}>AI模型</Title>
              <Paragraph>
                用于各种AI任务的微调模型和LoRA适配器，包括自然语言处理、计算机视觉和生成式AI。
              </Paragraph>
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={8}>
            <Card className="text-center h-full" hoverable>
              <div className="text-4xl mb-4">⚡</div>
              <Title level={4}>工作流</Title>
              <Paragraph>
                完整的AI工作流和自动化管道，可直接部署到您的应用程序中。
              </Paragraph>
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={8}>
            <Card className="text-center h-full" hoverable>
              <div className="text-4xl mb-4">💬</div>
              <Title level={4}>提示词和工具</Title>
              <Paragraph>
                为创作者、开发者和企业优化的提示词和实用AI工具。
              </Paragraph>
            </Card>
          </Col>
        </Row>
      </div>

      {/* Stats Section */}
      <div className="bg-gray-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Row justify="center" className="mb-16">
            <Col xs={24} lg={16} className="text-center">
              <Title level={2}>数千用户的信赖之选</Title>
              <Paragraph className="text-lg text-gray-600">
                加入我们不断壮大的AI创作者和开发者社区
              </Paragraph>
            </Col>
          </Row>

          <Row gutter={[32, 32]}>
            <Col xs={24} sm={8}>
              <Card className="text-center">
                <Statistic
                  title="AI资源"
                  value={1000}
                  suffix="+"
                  valueStyle={{ color: '#1890ff' }}
                  prefix={<ThunderboltOutlined />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={8}>
              <Card className="text-center">
                <Statistic
                  title="活跃创作者"
                  value={500}
                  suffix="+"
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<TeamOutlined />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={8}>
              <Card className="text-center">
                <Statistic
                  title="创作者收益"
                  value={50000}
                  prefix="$"
                  suffix="+"
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
          </Row>
        </div>
      </div>

      {/* Development Status */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <Card className="bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
          <Row justify="center">
            <Col xs={24} lg={20}>
              <div className="text-center mb-8">
                <Title level={3} className="text-yellow-800">
                  🚧 MVP开发进度
                </Title>
                <Paragraph className="text-yellow-700">
                  构建AI资源交易的未来
                </Paragraph>
              </div>

              <Row gutter={[24, 24]}>
                <Col xs={24} md={12}>
                  <Space direction="vertical" size="middle" className="w-full">
                    <div className="flex items-center justify-between">
                      <Text strong>
                        <CheckCircleOutlined className="text-green-500 mr-2" />
                        项目架构
                      </Text>
                      <Tag color="green">已完成</Tag>
                    </div>
                    <div className="flex items-center justify-between">
                      <Text strong>
                        <CheckCircleOutlined className="text-green-500 mr-2" />
                        数据库设计
                      </Text>
                      <Tag color="green">已完成</Tag>
                    </div>
                    <div className="flex items-center justify-between">
                      <Text strong>
                        <CheckCircleOutlined className="text-green-500 mr-2" />
                        API架构
                      </Text>
                      <Tag color="green">已完成</Tag>
                    </div>
                    <div className="flex items-center justify-between">
                      <Text strong>
                        <CheckCircleOutlined className="text-green-500 mr-2" />
                        核心功能
                      </Text>
                      <Tag color="green">已完成</Tag>
                    </div>
                  </Space>
                </Col>
                <Col xs={24} md={12}>
                  <Space direction="vertical" size="middle" className="w-full">
                    <div className="flex items-center justify-between">
                      <Text strong>
                        <CheckCircleOutlined className="text-green-500 mr-2" />
                        UI/UX设计
                      </Text>
                      <Tag color="green">已完成</Tag>
                    </div>
                    <div className="flex items-center justify-between">
                      <Text strong>
                        <SyncOutlined spin className="text-blue-500 mr-2" />
                        测试与质量保证
                      </Text>
                      <Tag color="processing">进行中</Tag>
                    </div>
                    <div className="flex items-center justify-between">
                      <Text strong>
                        <ClockCircleOutlined className="text-gray-500 mr-2" />
                        部署上线
                      </Text>
                      <Tag color="default">待开始</Tag>
                    </div>
                    <div className="flex items-center justify-between">
                      <Text strong>
                        <ClockCircleOutlined className="text-gray-500 mr-2" />
                        生产环境发布
                      </Text>
                      <Tag color="default">待开始</Tag>
                    </div>
                  </Space>
                </Col>
              </Row>

              <div className="mt-8">
                <Text strong className="block mb-2">总体进度</Text>
                <Progress
                  percent={85}
                  status="active"
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
              </div>
            </Col>
          </Row>
        </Card>
      </div>
    </Content>
  )
}
