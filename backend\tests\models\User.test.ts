import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { UserModel } from '../../src/models/User'
import { pool } from '../../src/services/database'

// Mock database pool
vi.mock('../../src/services/database', () => ({
  pool: {
    query: vi.fn()
  }
}))

describe('UserModel', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('create', () => {
    it('should create a new user', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'hashedpassword',
        fullName: 'Test User'
      }

      const mockResult = {
        rows: [{
          id: '1',
          ...userData,
          role: 'user',
          is_verified: false,
          created_at: new Date(),
          updated_at: new Date()
        }]
      }

      vi.mocked(pool.query).mockResolvedValue(mockResult)

      const result = await UserModel.create(userData)

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO users'),
        expect.arrayContaining([
          userData.username,
          userData.email,
          userData.password,
          userData.fullName
        ])
      )
      expect(result.id).toBe('1')
      expect(result.username).toBe(userData.username)
    })

    it('should handle database errors', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'hashedpassword',
        fullName: 'Test User'
      }

      vi.mocked(pool.query).mockRejectedValue(new Error('Database error'))

      await expect(UserModel.create(userData)).rejects.toThrow('Database error')
    })
  })

  describe('findById', () => {
    it('should find user by id', async () => {
      const userId = '1'
      const mockUser = {
        id: userId,
        username: 'testuser',
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'user',
        is_verified: false,
        created_at: new Date(),
        updated_at: new Date()
      }

      const mockResult = { rows: [mockUser] }
      vi.mocked(pool.query).mockResolvedValue(mockResult)

      const result = await UserModel.findById(userId)

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM users WHERE id = $1'),
        [userId]
      )
      expect(result?.id).toBe(userId)
      expect(result?.username).toBe(mockUser.username)
    })

    it('should return null if user not found', async () => {
      const userId = 'nonexistent'
      const mockResult = { rows: [] }
      vi.mocked(pool.query).mockResolvedValue(mockResult)

      const result = await UserModel.findById(userId)

      expect(result).toBeNull()
    })
  })

  describe('findByEmail', () => {
    it('should find user by email', async () => {
      const email = '<EMAIL>'
      const mockUser = {
        id: '1',
        username: 'testuser',
        email: email,
        full_name: 'Test User',
        role: 'user',
        is_verified: false,
        created_at: new Date(),
        updated_at: new Date()
      }

      const mockResult = { rows: [mockUser] }
      vi.mocked(pool.query).mockResolvedValue(mockResult)

      const result = await UserModel.findByEmail(email)

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM users WHERE email = $1'),
        [email]
      )
      expect(result?.email).toBe(email)
    })

    it('should return null if user not found', async () => {
      const email = '<EMAIL>'
      const mockResult = { rows: [] }
      vi.mocked(pool.query).mockResolvedValue(mockResult)

      const result = await UserModel.findByEmail(email)

      expect(result).toBeNull()
    })
  })

  describe('findByUsername', () => {
    it('should find user by username', async () => {
      const username = 'testuser'
      const mockUser = {
        id: '1',
        username: username,
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'user',
        is_verified: false,
        created_at: new Date(),
        updated_at: new Date()
      }

      const mockResult = { rows: [mockUser] }
      vi.mocked(pool.query).mockResolvedValue(mockResult)

      const result = await UserModel.findByUsername(username)

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM users WHERE username = $1'),
        [username]
      )
      expect(result?.username).toBe(username)
    })
  })

  describe('update', () => {
    it('should update user data', async () => {
      const userId = '1'
      const updateData = {
        fullName: 'Updated Name',
        avatarUrl: 'https://example.com/avatar.jpg'
      }

      const mockUpdatedUser = {
        id: userId,
        username: 'testuser',
        email: '<EMAIL>',
        full_name: updateData.fullName,
        avatar_url: updateData.avatarUrl,
        role: 'user',
        is_verified: false,
        created_at: new Date(),
        updated_at: new Date()
      }

      const mockResult = { rows: [mockUpdatedUser] }
      vi.mocked(pool.query).mockResolvedValue(mockResult)

      const result = await UserModel.update(userId, updateData)

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE users SET'),
        expect.arrayContaining([updateData.fullName, updateData.avatarUrl, userId])
      )
      expect(result?.fullName).toBe(updateData.fullName)
    })

    it('should return null if user not found', async () => {
      const userId = 'nonexistent'
      const updateData = { fullName: 'Updated Name' }
      const mockResult = { rows: [] }
      vi.mocked(pool.query).mockResolvedValue(mockResult)

      const result = await UserModel.update(userId, updateData)

      expect(result).toBeNull()
    })
  })

  describe('searchUsers', () => {
    it('should search users by query', async () => {
      const query = 'test'
      const limit = 10
      const offset = 0

      const mockUsers = [
        {
          id: '1',
          username: 'testuser1',
          email: '<EMAIL>',
          full_name: 'Test User 1',
          role: 'user',
          is_verified: false,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: '2',
          username: 'testuser2',
          email: '<EMAIL>',
          full_name: 'Test User 2',
          role: 'user',
          is_verified: true,
          created_at: new Date(),
          updated_at: new Date()
        }
      ]

      const mockResult = { rows: mockUsers }
      vi.mocked(pool.query).mockResolvedValue(mockResult)

      const result = await UserModel.searchUsers(query, limit, offset)

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('WHERE'),
        expect.arrayContaining([`%${query}%`, `%${query}%`, limit, offset])
      )
      expect(result).toHaveLength(2)
      expect(result[0].username).toBe('testuser1')
    })
  })
})
